{"name": "@types/react", "version": "18.3.12", "description": "TypeScript definitions for react", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "AssureSign", "url": "http://www.assuresign.com"}, {"name": "Microsoft", "url": "https://microsoft.com"}, {"name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "githubUsername": "bbenezech", "url": "https://github.com/bbenezech"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "pza<PERSON>linsky", "url": "https://github.com/pza<PERSON><PERSON>ky"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ericanderson"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON>", "githubUsername": "theruther4d", "url": "https://github.com/theruther4d"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/guil<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "githubUsername": "ferd<PERSON><PERSON>", "url": "https://github.com/ferdaber"}, {"name": "<PERSON>", "githubUsername": "jrakotoharisoa", "url": "https://github.com/jrakotoharisoa"}, {"name": "<PERSON>", "githubUsername": "pascal<PERSON>v", "url": "https://github.com/pascaloliv"}, {"name": "<PERSON>", "githubUsername": "hotell", "url": "https://github.com/hotell"}, {"name": "<PERSON>", "githubUsername": "frank<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/franklixuefei"}, {"name": "<PERSON>", "githubUsername": "Jessidhia", "url": "https://github.com/Jessidhia"}, {"name": "Saransh Kataria", "githubUsername": "saranshkataria", "url": "https://github.com/saranshkataria"}, {"name": "Kanitkorn Sujautra", "githubUsername": "luk<PERSON><PERSON>", "url": "https://github.com/lukyth"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "githubUsername": "zieka", "url": "https://github.com/zieka"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dancerphil"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/dimitrop<PERSON>los"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "disju<PERSON><PERSON>", "url": "https://github.com/disjukr"}, {"name": "<PERSON>", "githubUsername": "vhfmag", "url": "https://github.com/vhfmag"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/priyanshurav"}, {"name": "<PERSON>", "githubUsername": "Semigradsky", "url": "https://github.com/Semigradsky"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mattpocock"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=5.0": {"*": ["ts5.0/*"]}}, "exports": {".": {"types@<=5.0": {"default": "./ts5.0/index.d.ts"}, "types": {"default": "./index.d.ts"}}, "./canary": {"types@<=5.0": {"default": "./ts5.0/canary.d.ts"}, "types": {"default": "./canary.d.ts"}}, "./experimental": {"types@<=5.0": {"default": "./ts5.0/experimental.d.ts"}, "types": {"default": "./experimental.d.ts"}}, "./jsx-runtime": {"types@<=5.0": {"default": "./ts5.0/jsx-runtime.d.ts"}, "types": {"default": "./jsx-runtime.d.ts"}}, "./jsx-dev-runtime": {"types@<=5.0": {"default": "./ts5.0/jsx-dev-runtime.d.ts"}, "types": {"default": "./jsx-dev-runtime.d.ts"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react"}, "scripts": {}, "dependencies": {"@types/prop-types": "*", "csstype": "^3.0.2"}, "peerDependencies": {}, "typesPublisherContentHash": "d59942da5433cf6c9d66442070074fa48ef9c823a4175da6e4d183d0a70ccc72", "typeScriptVersion": "4.8"}