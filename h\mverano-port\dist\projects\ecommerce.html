<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Commerce Platform</title>
    <style>
        body {
            font-family: <PERSON><PERSON><PERSON>, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #316AC5;
            color: black;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #ECE9D8;
            padding: 20px;
            border: 2px outset #ECE9D8;
            box-shadow: 2px 2px 5px #808080;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 2em;
            color: #000080;
            text-decoration: underline;
        }
        .project-info {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .info-card {
            background: #F0F0F0;
            padding: 15px;
            border: 2px inset #ECE9D8;
            margin-bottom: 15px;
        }
        .tech-stack {
            margin-top: 10px;
        }
        .tech-tag {
            background: #DBEAFE;
            padding: 3px 8px;
            border: 1px solid #3B82F6;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }
        .features {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        .features li {
            padding: 5px 0;
            border-bottom: 1px solid #C0C0C0;
        }
        .features li:before {
            content: "• ";
            color: #000080;
            font-weight: bold;
        }
        .back-btn {
            background: #ECE9D8;
            color: black;
            border: 2px outset #ECE9D8;
            padding: 5px 15px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
            font-family: Tahoma, Arial, sans-serif;
        }
        .back-btn:hover {
            background: #D4D0C8;
        }
        h3 {
            color: #000080;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>E-Commerce Web Platform</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            A comprehensive full-stack e-commerce solution with modern features and scalable architecture.
        </p>
        
        <div class="project-info">
            <div class="info-card">
                <h3>🛠️ Technology Stack</h3>
                <div class="tech-stack">
                    <span class="tech-tag">React</span>
                    <span class="tech-tag">Node.js</span>
                    <span class="tech-tag">PostgreSQL</span>
                    <span class="tech-tag">Express.js</span>
                    <span class="tech-tag">Redis</span>
                    <span class="tech-tag">AWS</span>
                </div>
            </div>
            
            <div class="info-card">
                <h3>📊 Project Status</h3>
                <p><strong>Status:</strong> Production</p>
                <p><strong>Version:</strong> 4.1.2</p>
                <p><strong>Users:</strong> 10K+ registered users</p>
                <p><strong>Performance:</strong> 99.9% uptime</p>
            </div>
        </div>
        
        <div class="info-card">
            <h3>✨ Key Features</h3>
            <ul class="features">
                <li>User authentication and authorization system</li>
                <li>Secure payment processing with multiple gateways</li>
                <li>Comprehensive admin dashboard and analytics</li>
                <li>Product catalog with advanced search and filtering</li>
                <li>Shopping cart and wishlist functionality</li>
                <li>Order tracking and management system</li>
                <li>Inventory management and stock alerts</li>
                <li>Customer reviews and rating system</li>
                <li>Email notifications and marketing automation</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3>📝 Description</h3>
            <p>
                A robust, scalable e-commerce platform built with modern web technologies. This full-stack 
                application provides a complete online shopping experience with features for both customers 
                and administrators.
            </p>
            <p>
                The platform includes secure user authentication, payment processing, inventory management, 
                and a powerful admin dashboard for managing products, orders, and customers. Built with 
                performance and security in mind, it can handle high traffic loads and provides a seamless 
                shopping experience across all devices.
            </p>
        </div>
        
        <a href="javascript:history.back()" class="back-btn">← Back to Projects</a>
    </div>
</body>
</html>
