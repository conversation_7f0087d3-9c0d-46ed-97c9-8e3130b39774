import{r as l,a as te,b as Ft,G as Rn,R as On}from"./vendor.B23LvuPX.js";var It={exports:{}},De={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nn=l,Tn=Symbol.for("react.element"),Mn=Symbol.for("react.fragment"),Ln=Object.prototype.hasOwnProperty,Dn=Nn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,kn={key:!0,ref:!0,__self:!0,__source:!0};function Wt(e,t,n){var r,o={},i=null,c=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(c=t.ref);for(r in t)Ln.call(t,r)&&!kn.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Tn,type:e,key:i,ref:c,props:o,_owner:Dn.current}}De.Fragment=Mn;De.jsx=Wt;De.jsxs=Wt;It.exports=De;var R=It.exports;function We(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function _n(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function $t(...e){return t=>e.forEach(n=>_n(n,t))}function q(...e){return l.useCallback($t(...e),e)}function Fn(e,t=[]){let n=[];function r(i,c){const s=l.createContext(c),u=n.length;n=[...n,c];function a(d){const{scope:p,children:h,...v}=d,f=(p==null?void 0:p[e][u])||s,g=l.useMemo(()=>v,Object.values(v));return R.jsx(f.Provider,{value:g,children:h})}function m(d,p){const h=(p==null?void 0:p[e][u])||s,v=l.useContext(h);if(v)return v;if(c!==void 0)return c;throw new Error(`\`${d}\` must be used within \`${i}\``)}return a.displayName=i+"Provider",[a,m]}const o=()=>{const i=n.map(c=>l.createContext(c));return function(s){const u=(s==null?void 0:s[e])||i;return l.useMemo(()=>({[`__scope${e}`]:{...s,[e]:u}}),[s,u])}};return o.scopeName=e,[r,In(o,...t)]}function In(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const c=r.reduce((s,{useScope:u,scopeName:a})=>{const d=u(i)[`__scope${a}`];return{...s,...d}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}var Oe=l.forwardRef((e,t)=>{const{children:n,...r}=e,o=l.Children.toArray(n),i=o.find($n);if(i){const c=i.props.children,s=o.map(u=>u===i?l.Children.count(c)>1?l.Children.only(null):l.isValidElement(c)?c.props.children:null:u);return R.jsx(Ke,{...r,ref:t,children:l.isValidElement(c)?l.cloneElement(c,void 0,s):null})}return R.jsx(Ke,{...r,ref:t,children:n})});Oe.displayName="Slot";var Ke=l.forwardRef((e,t)=>{const{children:n,...r}=e;if(l.isValidElement(n)){const o=jn(n);return l.cloneElement(n,{...Bn(r,n.props),ref:t?$t(t,o):o})}return l.Children.count(n)>1?l.Children.only(null):null});Ke.displayName="SlotClone";var Wn=({children:e})=>R.jsx(R.Fragment,{children:e});function $n(e){return l.isValidElement(e)&&e.type===Wn}function Bn(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...s)=>{i(...s),o(...s)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function jn(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function di(e){const t=e+"CollectionProvider",[n,r]=Fn(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=h=>{const{scope:v,children:f}=h,g=te.useRef(null),y=te.useRef(new Map).current;return R.jsx(o,{scope:v,itemMap:y,collectionRef:g,children:f})};c.displayName=t;const s=e+"CollectionSlot",u=te.forwardRef((h,v)=>{const{scope:f,children:g}=h,y=i(s,f),w=q(v,y.collectionRef);return R.jsx(Oe,{ref:w,children:g})});u.displayName=s;const a=e+"CollectionItemSlot",m="data-radix-collection-item",d=te.forwardRef((h,v)=>{const{scope:f,children:g,...y}=h,w=te.useRef(null),b=q(v,w),x=i(a,f);return te.useEffect(()=>(x.itemMap.set(w,{ref:w,...y}),()=>void x.itemMap.delete(w))),R.jsx(Oe,{[m]:"",ref:b,children:g})});d.displayName=a;function p(h){const v=i(e+"CollectionConsumer",h);return te.useCallback(()=>{const g=v.collectionRef.current;if(!g)return[];const y=Array.from(g.querySelectorAll(`[${m}]`));return Array.from(v.itemMap.values()).sort((x,E)=>y.indexOf(x.ref.current)-y.indexOf(E.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:c,Slot:u,ItemSlot:d},p,r]}function mi(e,t=[]){let n=[];function r(i,c){const s=l.createContext(c),u=n.length;n=[...n,c];const a=d=>{var y;const{scope:p,children:h,...v}=d,f=((y=p==null?void 0:p[e])==null?void 0:y[u])||s,g=l.useMemo(()=>v,Object.values(v));return R.jsx(f.Provider,{value:g,children:h})};a.displayName=i+"Provider";function m(d,p){var f;const h=((f=p==null?void 0:p[e])==null?void 0:f[u])||s,v=l.useContext(h);if(v)return v;if(c!==void 0)return c;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[a,m]}const o=()=>{const i=n.map(c=>l.createContext(c));return function(s){const u=(s==null?void 0:s[e])||i;return l.useMemo(()=>({[`__scope${e}`]:{...s,[e]:u}}),[s,u])}};return o.scopeName=e,[r,Hn(o,...t)]}function Hn(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const c=r.reduce((s,{useScope:u,scopeName:a})=>{const d=u(i)[`__scope${a}`];return{...s,...d}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}var Un=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],oe=Un.reduce((e,t)=>{const n=l.forwardRef((r,o)=>{const{asChild:i,...c}=r,s=i?Oe:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),R.jsx(s,{...c,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Vn(e,t){e&&Ft.flushSync(()=>e.dispatchEvent(t))}function Z(e){const t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function zn(e,t=globalThis==null?void 0:globalThis.document){const n=Z(e);l.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Yn="DismissableLayer",qe="dismissableLayer.update",Xn="dismissableLayer.pointerDownOutside",Kn="dismissableLayer.focusOutside",vt,Bt=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),jt=l.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:c,onDismiss:s,...u}=e,a=l.useContext(Bt),[m,d]=l.useState(null),p=(m==null?void 0:m.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,h]=l.useState({}),v=q(t,S=>d(S)),f=Array.from(a.layers),[g]=[...a.layersWithOutsidePointerEventsDisabled].slice(-1),y=f.indexOf(g),w=m?f.indexOf(m):-1,b=a.layersWithOutsidePointerEventsDisabled.size>0,x=w>=y,E=Zn(S=>{const P=S.target,T=[...a.branches].some(O=>O.contains(P));!x||T||(o==null||o(S),c==null||c(S),S.defaultPrevented||s==null||s())},p),C=Gn(S=>{const P=S.target;[...a.branches].some(O=>O.contains(P))||(i==null||i(S),c==null||c(S),S.defaultPrevented||s==null||s())},p);return zn(S=>{w===a.layers.size-1&&(r==null||r(S),!S.defaultPrevented&&s&&(S.preventDefault(),s()))},p),l.useEffect(()=>{if(m)return n&&(a.layersWithOutsidePointerEventsDisabled.size===0&&(vt=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),a.layersWithOutsidePointerEventsDisabled.add(m)),a.layers.add(m),gt(),()=>{n&&a.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=vt)}},[m,p,n,a]),l.useEffect(()=>()=>{m&&(a.layers.delete(m),a.layersWithOutsidePointerEventsDisabled.delete(m),gt())},[m,a]),l.useEffect(()=>{const S=()=>h({});return document.addEventListener(qe,S),()=>document.removeEventListener(qe,S)},[]),R.jsx(oe.div,{...u,ref:v,style:{pointerEvents:b?x?"auto":"none":void 0,...e.style},onFocusCapture:We(e.onFocusCapture,C.onFocusCapture),onBlurCapture:We(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:We(e.onPointerDownCapture,E.onPointerDownCapture)})});jt.displayName=Yn;var qn="DismissableLayerBranch",Ht=l.forwardRef((e,t)=>{const n=l.useContext(Bt),r=l.useRef(null),o=q(t,r);return l.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),R.jsx(oe.div,{...e,ref:o})});Ht.displayName=qn;function Zn(e,t=globalThis==null?void 0:globalThis.document){const n=Z(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{const i=s=>{if(s.target&&!r.current){let u=function(){Ut(Xn,n,a,{discrete:!0})};const a={originalEvent:s};s.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Gn(e,t=globalThis==null?void 0:globalThis.document){const n=Z(e),r=l.useRef(!1);return l.useEffect(()=>{const o=i=>{i.target&&!r.current&&Ut(Kn,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function gt(){const e=new CustomEvent(qe);document.dispatchEvent(e)}function Ut(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Vn(o,i):o.dispatchEvent(i)}var hi=jt,pi=Ht,ne=globalThis!=null&&globalThis.document?l.useLayoutEffect:()=>{},Qn="Portal",Jn=l.forwardRef((e,t)=>{var s;const{container:n,...r}=e,[o,i]=l.useState(!1);ne(()=>i(!0),[]);const c=n||o&&((s=globalThis==null?void 0:globalThis.document)==null?void 0:s.body);return c?Rn.createPortal(R.jsx(oe.div,{...r,ref:t}),c):null});Jn.displayName=Qn;function er(e,t){return l.useReducer((n,r)=>t[n][r]??n,e)}var tr=e=>{const{present:t,children:n}=e,r=nr(t),o=typeof n=="function"?n({present:r.isPresent}):l.Children.only(n),i=q(r.ref,rr(o));return typeof n=="function"||r.isPresent?l.cloneElement(o,{ref:i}):null};tr.displayName="Presence";function nr(e){const[t,n]=l.useState(),r=l.useRef({}),o=l.useRef(e),i=l.useRef("none"),c=e?"mounted":"unmounted",[s,u]=er(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return l.useEffect(()=>{const a=we(r.current);i.current=s==="mounted"?a:"none"},[s]),ne(()=>{const a=r.current,m=o.current;if(m!==e){const p=i.current,h=we(a);e?u("MOUNT"):h==="none"||(a==null?void 0:a.display)==="none"?u("UNMOUNT"):u(m&&p!==h?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,u]),ne(()=>{if(t){let a;const m=t.ownerDocument.defaultView??window,d=h=>{const f=we(r.current).includes(h.animationName);if(h.target===t&&f&&(u("ANIMATION_END"),!o.current)){const g=t.style.animationFillMode;t.style.animationFillMode="forwards",a=m.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=g)})}},p=h=>{h.target===t&&(i.current=we(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{m.clearTimeout(a),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:l.useCallback(a=>{a&&(r.current=getComputedStyle(a)),n(a)},[])}}function we(e){return(e==null?void 0:e.animationName)||"none"}function rr(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function vi({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=or({defaultProp:t,onChange:n}),i=e!==void 0,c=i?e:r,s=Z(n),u=l.useCallback(a=>{if(i){const d=typeof a=="function"?a(e):a;d!==e&&s(d)}else o(a)},[i,e,o,s]);return[c,u]}function or({defaultProp:e,onChange:t}){const n=l.useState(e),[r]=n,o=l.useRef(r),i=Z(t);return l.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}var ir=On.useId||(()=>{}),sr=0;function gi(e){const[t,n]=l.useState(ir());return ne(()=>{e||n(r=>r??String(sr++))},[e]),e||(t?`radix-${t}`:"")}const cr=["top","right","bottom","left"],G=Math.min,I=Math.max,Ne=Math.round,xe=Math.floor,Q=e=>({x:e,y:e}),ar={left:"right",right:"left",bottom:"top",top:"bottom"},lr={start:"end",end:"start"};function Ze(e,t,n){return I(e,G(t,n))}function Y(e,t){return typeof e=="function"?e(t):e}function X(e){return e.split("-")[0]}function fe(e){return e.split("-")[1]}function tt(e){return e==="x"?"y":"x"}function nt(e){return e==="y"?"height":"width"}function J(e){return["top","bottom"].includes(X(e))?"y":"x"}function rt(e){return tt(J(e))}function ur(e,t,n){n===void 0&&(n=!1);const r=fe(e),o=rt(e),i=nt(o);let c=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(c=Te(c)),[c,Te(c)]}function fr(e){const t=Te(e);return[Ge(e),t,Ge(t)]}function Ge(e){return e.replace(/start|end/g,t=>lr[t])}function dr(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],c=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:c;default:return[]}}function mr(e,t,n,r){const o=fe(e);let i=dr(X(e),n==="start",r);return o&&(i=i.map(c=>c+"-"+o),t&&(i=i.concat(i.map(Ge)))),i}function Te(e){return e.replace(/left|right|bottom|top/g,t=>ar[t])}function hr(e){return{top:0,right:0,bottom:0,left:0,...e}}function Vt(e){return typeof e!="number"?hr(e):{top:e,right:e,bottom:e,left:e}}function Me(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function yt(e,t,n){let{reference:r,floating:o}=e;const i=J(t),c=rt(t),s=nt(c),u=X(t),a=i==="y",m=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,p=r[s]/2-o[s]/2;let h;switch(u){case"top":h={x:m,y:r.y-o.height};break;case"bottom":h={x:m,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:d};break;case"left":h={x:r.x-o.width,y:d};break;default:h={x:r.x,y:r.y}}switch(fe(t)){case"start":h[c]-=p*(n&&a?-1:1);break;case"end":h[c]+=p*(n&&a?-1:1);break}return h}const pr=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:c}=n,s=i.filter(Boolean),u=await(c.isRTL==null?void 0:c.isRTL(t));let a=await c.getElementRects({reference:e,floating:t,strategy:o}),{x:m,y:d}=yt(a,r,u),p=r,h={},v=0;for(let f=0;f<s.length;f++){const{name:g,fn:y}=s[f],{x:w,y:b,data:x,reset:E}=await y({x:m,y:d,initialPlacement:r,placement:p,strategy:o,middlewareData:h,rects:a,platform:c,elements:{reference:e,floating:t}});m=w??m,d=b??d,h={...h,[g]:{...h[g],...x}},E&&v<=50&&(v++,typeof E=="object"&&(E.placement&&(p=E.placement),E.rects&&(a=E.rects===!0?await c.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:m,y:d}=yt(a,p,u)),f=-1)}return{x:m,y:d,placement:p,strategy:o,middlewareData:h}};async function he(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:c,elements:s,strategy:u}=e,{boundary:a="clippingAncestors",rootBoundary:m="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=Y(t,e),v=Vt(h),g=s[p?d==="floating"?"reference":"floating":d],y=Me(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(g)))==null||n?g:g.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(s.floating)),boundary:a,rootBoundary:m,strategy:u})),w=d==="floating"?{x:r,y:o,width:c.floating.width,height:c.floating.height}:c.reference,b=await(i.getOffsetParent==null?void 0:i.getOffsetParent(s.floating)),x=await(i.isElement==null?void 0:i.isElement(b))?await(i.getScale==null?void 0:i.getScale(b))||{x:1,y:1}:{x:1,y:1},E=Me(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:w,offsetParent:b,strategy:u}):w);return{top:(y.top-E.top+v.top)/x.y,bottom:(E.bottom-y.bottom+v.bottom)/x.y,left:(y.left-E.left+v.left)/x.x,right:(E.right-y.right+v.right)/x.x}}const vr=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:c,elements:s,middlewareData:u}=t,{element:a,padding:m=0}=Y(e,t)||{};if(a==null)return{};const d=Vt(m),p={x:n,y:r},h=rt(o),v=nt(h),f=await c.getDimensions(a),g=h==="y",y=g?"top":"left",w=g?"bottom":"right",b=g?"clientHeight":"clientWidth",x=i.reference[v]+i.reference[h]-p[h]-i.floating[v],E=p[h]-i.reference[h],C=await(c.getOffsetParent==null?void 0:c.getOffsetParent(a));let S=C?C[b]:0;(!S||!await(c.isElement==null?void 0:c.isElement(C)))&&(S=s.floating[b]||i.floating[v]);const P=x/2-E/2,T=S/2-f[v]/2-1,O=G(d[y],T),_=G(d[w],T),F=O,M=S-f[v]-_,N=S/2-f[v]/2+P,$=Ze(F,N,M),L=!u.arrow&&fe(o)!=null&&N!==$&&i.reference[v]/2-(N<F?O:_)-f[v]/2<0,D=L?N<F?N-F:N-M:0;return{[h]:p[h]+D,data:{[h]:$,centerOffset:N-$-D,...L&&{alignmentOffset:D}},reset:L}}}),gr=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:c,initialPlacement:s,platform:u,elements:a}=t,{mainAxis:m=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:f=!0,...g}=Y(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const y=X(o),w=J(s),b=X(s)===s,x=await(u.isRTL==null?void 0:u.isRTL(a.floating)),E=p||(b||!f?[Te(s)]:fr(s)),C=v!=="none";!p&&C&&E.push(...mr(s,f,v,x));const S=[s,...E],P=await he(t,g),T=[];let O=((r=i.flip)==null?void 0:r.overflows)||[];if(m&&T.push(P[y]),d){const N=ur(o,c,x);T.push(P[N[0]],P[N[1]])}if(O=[...O,{placement:o,overflows:T}],!T.every(N=>N<=0)){var _,F;const N=(((_=i.flip)==null?void 0:_.index)||0)+1,$=S[N];if($)return{data:{index:N,overflows:O},reset:{placement:$}};let L=(F=O.filter(D=>D.overflows[0]<=0).sort((D,A)=>D.overflows[1]-A.overflows[1])[0])==null?void 0:F.placement;if(!L)switch(h){case"bestFit":{var M;const D=(M=O.filter(A=>{if(C){const k=J(A.placement);return k===w||k==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(k=>k>0).reduce((k,H)=>k+H,0)]).sort((A,k)=>A[1]-k[1])[0])==null?void 0:M[0];D&&(L=D);break}case"initialPlacement":L=s;break}if(o!==L)return{reset:{placement:L}}}return{}}}};function wt(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function xt(e){return cr.some(t=>e[t]>=0)}const yr=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Y(e,t);switch(r){case"referenceHidden":{const i=await he(t,{...o,elementContext:"reference"}),c=wt(i,n.reference);return{data:{referenceHiddenOffsets:c,referenceHidden:xt(c)}}}case"escaped":{const i=await he(t,{...o,altBoundary:!0}),c=wt(i,n.floating);return{data:{escapedOffsets:c,escaped:xt(c)}}}default:return{}}}}};async function wr(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),c=X(n),s=fe(n),u=J(n)==="y",a=["left","top"].includes(c)?-1:1,m=i&&u?-1:1,d=Y(t,e);let{mainAxis:p,crossAxis:h,alignmentAxis:v}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&typeof v=="number"&&(h=s==="end"?v*-1:v),u?{x:h*m,y:p*a}:{x:p*a,y:h*m}}const xr=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:c,middlewareData:s}=t,u=await wr(t,e);return c===((n=s.offset)==null?void 0:n.placement)&&(r=s.arrow)!=null&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:c}}}}},br=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:c=!1,limiter:s={fn:g=>{let{x:y,y:w}=g;return{x:y,y:w}}},...u}=Y(e,t),a={x:n,y:r},m=await he(t,u),d=J(X(o)),p=tt(d);let h=a[p],v=a[d];if(i){const g=p==="y"?"top":"left",y=p==="y"?"bottom":"right",w=h+m[g],b=h-m[y];h=Ze(w,h,b)}if(c){const g=d==="y"?"top":"left",y=d==="y"?"bottom":"right",w=v+m[g],b=v-m[y];v=Ze(w,v,b)}const f=s.fn({...t,[p]:h,[d]:v});return{...f,data:{x:f.x-n,y:f.y-r,enabled:{[p]:i,[d]:c}}}}}},Er=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:c}=t,{offset:s=0,mainAxis:u=!0,crossAxis:a=!0}=Y(e,t),m={x:n,y:r},d=J(o),p=tt(d);let h=m[p],v=m[d];const f=Y(s,t),g=typeof f=="number"?{mainAxis:f,crossAxis:0}:{mainAxis:0,crossAxis:0,...f};if(u){const b=p==="y"?"height":"width",x=i.reference[p]-i.floating[b]+g.mainAxis,E=i.reference[p]+i.reference[b]-g.mainAxis;h<x?h=x:h>E&&(h=E)}if(a){var y,w;const b=p==="y"?"width":"height",x=["top","left"].includes(X(o)),E=i.reference[d]-i.floating[b]+(x&&((y=c.offset)==null?void 0:y[d])||0)+(x?0:g.crossAxis),C=i.reference[d]+i.reference[b]+(x?0:((w=c.offset)==null?void 0:w[d])||0)-(x?g.crossAxis:0);v<E?v=E:v>C&&(v=C)}return{[p]:h,[d]:v}}}},Sr=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:c,elements:s}=t,{apply:u=()=>{},...a}=Y(e,t),m=await he(t,a),d=X(o),p=fe(o),h=J(o)==="y",{width:v,height:f}=i.floating;let g,y;d==="top"||d==="bottom"?(g=d,y=p===(await(c.isRTL==null?void 0:c.isRTL(s.floating))?"start":"end")?"left":"right"):(y=d,g=p==="end"?"top":"bottom");const w=f-m.top-m.bottom,b=v-m.left-m.right,x=G(f-m[g],w),E=G(v-m[y],b),C=!t.middlewareData.shift;let S=x,P=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=b),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(S=w),C&&!p){const O=I(m.left,0),_=I(m.right,0),F=I(m.top,0),M=I(m.bottom,0);h?P=v-2*(O!==0||_!==0?O+_:I(m.left,m.right)):S=f-2*(F!==0||M!==0?F+M:I(m.top,m.bottom))}await u({...t,availableWidth:P,availableHeight:S});const T=await c.getDimensions(s.floating);return v!==T.width||f!==T.height?{reset:{rects:!0}}:{}}}};function ke(){return typeof window<"u"}function de(e){return zt(e)?(e.nodeName||"").toLowerCase():"#document"}function W(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function z(e){var t;return(t=(zt(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function zt(e){return ke()?e instanceof Node||e instanceof W(e).Node:!1}function B(e){return ke()?e instanceof Element||e instanceof W(e).Element:!1}function V(e){return ke()?e instanceof HTMLElement||e instanceof W(e).HTMLElement:!1}function bt(e){return!ke()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof W(e).ShadowRoot}function ve(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=j(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Cr(e){return["table","td","th"].includes(de(e))}function _e(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function ot(e){const t=it(),n=B(e)?j(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Ar(e){let t=ee(e);for(;V(t)&&!ue(t);){if(ot(t))return t;if(_e(t))return null;t=ee(t)}return null}function it(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ue(e){return["html","body","#document"].includes(de(e))}function j(e){return W(e).getComputedStyle(e)}function Fe(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if(de(e)==="html")return e;const t=e.assignedSlot||e.parentNode||bt(e)&&e.host||z(e);return bt(t)?t.host:t}function Yt(e){const t=ee(e);return ue(t)?e.ownerDocument?e.ownerDocument.body:e.body:V(t)&&ve(t)?t:Yt(t)}function pe(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Yt(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),c=W(o);if(i){const s=Qe(c);return t.concat(c,c.visualViewport||[],ve(o)?o:[],s&&n?pe(s):[])}return t.concat(o,pe(o,[],n))}function Qe(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Xt(e){const t=j(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=V(e),i=o?e.offsetWidth:n,c=o?e.offsetHeight:r,s=Ne(n)!==i||Ne(r)!==c;return s&&(n=i,r=c),{width:n,height:r,$:s}}function st(e){return B(e)?e:e.contextElement}function ae(e){const t=st(e);if(!V(t))return Q(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Xt(t);let c=(i?Ne(n.width):n.width)/r,s=(i?Ne(n.height):n.height)/o;return(!c||!Number.isFinite(c))&&(c=1),(!s||!Number.isFinite(s))&&(s=1),{x:c,y:s}}const Pr=Q(0);function Kt(e){const t=W(e);return!it()||!t.visualViewport?Pr:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Rr(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==W(e)?!1:t}function re(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=st(e);let c=Q(1);t&&(r?B(r)&&(c=ae(r)):c=ae(e));const s=Rr(i,n,r)?Kt(i):Q(0);let u=(o.left+s.x)/c.x,a=(o.top+s.y)/c.y,m=o.width/c.x,d=o.height/c.y;if(i){const p=W(i),h=r&&B(r)?W(r):r;let v=p,f=Qe(v);for(;f&&r&&h!==v;){const g=ae(f),y=f.getBoundingClientRect(),w=j(f),b=y.left+(f.clientLeft+parseFloat(w.paddingLeft))*g.x,x=y.top+(f.clientTop+parseFloat(w.paddingTop))*g.y;u*=g.x,a*=g.y,m*=g.x,d*=g.y,u+=b,a+=x,v=W(f),f=Qe(v)}}return Me({width:m,height:d,x:u,y:a})}function Or(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",c=z(r),s=t?_e(t.floating):!1;if(r===c||s&&i)return n;let u={scrollLeft:0,scrollTop:0},a=Q(1);const m=Q(0),d=V(r);if((d||!d&&!i)&&((de(r)!=="body"||ve(c))&&(u=Fe(r)),V(r))){const p=re(r);a=ae(r),m.x=p.x+r.clientLeft,m.y=p.y+r.clientTop}return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-u.scrollLeft*a.x+m.x,y:n.y*a.y-u.scrollTop*a.y+m.y}}function Nr(e){return Array.from(e.getClientRects())}function Je(e,t){const n=Fe(e).scrollLeft;return t?t.left+n:re(z(e)).left+n}function Tr(e){const t=z(e),n=Fe(e),r=e.ownerDocument.body,o=I(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=I(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let c=-n.scrollLeft+Je(e);const s=-n.scrollTop;return j(r).direction==="rtl"&&(c+=I(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:c,y:s}}function Mr(e,t){const n=W(e),r=z(e),o=n.visualViewport;let i=r.clientWidth,c=r.clientHeight,s=0,u=0;if(o){i=o.width,c=o.height;const a=it();(!a||a&&t==="fixed")&&(s=o.offsetLeft,u=o.offsetTop)}return{width:i,height:c,x:s,y:u}}function Lr(e,t){const n=re(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=V(e)?ae(e):Q(1),c=e.clientWidth*i.x,s=e.clientHeight*i.y,u=o*i.x,a=r*i.y;return{width:c,height:s,x:u,y:a}}function Et(e,t,n){let r;if(t==="viewport")r=Mr(e,n);else if(t==="document")r=Tr(z(e));else if(B(t))r=Lr(t,n);else{const o=Kt(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Me(r)}function qt(e,t){const n=ee(e);return n===t||!B(n)||ue(n)?!1:j(n).position==="fixed"||qt(n,t)}function Dr(e,t){const n=t.get(e);if(n)return n;let r=pe(e,[],!1).filter(s=>B(s)&&de(s)!=="body"),o=null;const i=j(e).position==="fixed";let c=i?ee(e):e;for(;B(c)&&!ue(c);){const s=j(c),u=ot(c);!u&&s.position==="fixed"&&(o=null),(i?!u&&!o:!u&&s.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||ve(c)&&!u&&qt(e,c))?r=r.filter(m=>m!==c):o=s,c=ee(c)}return t.set(e,r),r}function kr(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const c=[...n==="clippingAncestors"?_e(t)?[]:Dr(t,this._c):[].concat(n),r],s=c[0],u=c.reduce((a,m)=>{const d=Et(t,m,o);return a.top=I(d.top,a.top),a.right=G(d.right,a.right),a.bottom=G(d.bottom,a.bottom),a.left=I(d.left,a.left),a},Et(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function _r(e){const{width:t,height:n}=Xt(e);return{width:t,height:n}}function Fr(e,t,n){const r=V(t),o=z(t),i=n==="fixed",c=re(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const u=Q(0);if(r||!r&&!i)if((de(t)!=="body"||ve(o))&&(s=Fe(t)),r){const h=re(t,!0,i,t);u.x=h.x+t.clientLeft,u.y=h.y+t.clientTop}else o&&(u.x=Je(o));let a=0,m=0;if(o&&!r&&!i){const h=o.getBoundingClientRect();m=h.top+s.scrollTop,a=h.left+s.scrollLeft-Je(o,h)}const d=c.left+s.scrollLeft-u.x-a,p=c.top+s.scrollTop-u.y-m;return{x:d,y:p,width:c.width,height:c.height}}function $e(e){return j(e).position==="static"}function St(e,t){if(!V(e)||j(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return z(e)===n&&(n=n.ownerDocument.body),n}function Zt(e,t){const n=W(e);if(_e(e))return n;if(!V(e)){let o=ee(e);for(;o&&!ue(o);){if(B(o)&&!$e(o))return o;o=ee(o)}return n}let r=St(e,t);for(;r&&Cr(r)&&$e(r);)r=St(r,t);return r&&ue(r)&&$e(r)&&!ot(r)?n:r||Ar(e)||n}const Ir=async function(e){const t=this.getOffsetParent||Zt,n=this.getDimensions,r=await n(e.floating);return{reference:Fr(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Wr(e){return j(e).direction==="rtl"}const $r={convertOffsetParentRelativeRectToViewportRelativeRect:Or,getDocumentElement:z,getClippingRect:kr,getOffsetParent:Zt,getElementRects:Ir,getClientRects:Nr,getDimensions:_r,getScale:ae,isElement:B,isRTL:Wr};function Br(e,t){let n=null,r;const o=z(e);function i(){var s;clearTimeout(r),(s=n)==null||s.disconnect(),n=null}function c(s,u){s===void 0&&(s=!1),u===void 0&&(u=1),i();const{left:a,top:m,width:d,height:p}=e.getBoundingClientRect();if(s||t(),!d||!p)return;const h=xe(m),v=xe(o.clientWidth-(a+d)),f=xe(o.clientHeight-(m+p)),g=xe(a),w={rootMargin:-h+"px "+-v+"px "+-f+"px "+-g+"px",threshold:I(0,G(1,u))||1};let b=!0;function x(E){const C=E[0].intersectionRatio;if(C!==u){if(!b)return c();C?c(!1,C):r=setTimeout(()=>{c(!1,1e-7)},1e3)}b=!1}try{n=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch{n=new IntersectionObserver(x,w)}n.observe(e)}return c(!0),i}function jr(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:c=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,a=st(e),m=o||i?[...a?pe(a):[],...pe(t)]:[];m.forEach(y=>{o&&y.addEventListener("scroll",n,{passive:!0}),i&&y.addEventListener("resize",n)});const d=a&&s?Br(a,n):null;let p=-1,h=null;c&&(h=new ResizeObserver(y=>{let[w]=y;w&&w.target===a&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var b;(b=h)==null||b.observe(t)})),n()}),a&&!u&&h.observe(a),h.observe(t));let v,f=u?re(e):null;u&&g();function g(){const y=re(e);f&&(y.x!==f.x||y.y!==f.y||y.width!==f.width||y.height!==f.height)&&n(),f=y,v=requestAnimationFrame(g)}return n(),()=>{var y;m.forEach(w=>{o&&w.removeEventListener("scroll",n),i&&w.removeEventListener("resize",n)}),d==null||d(),(y=h)==null||y.disconnect(),h=null,u&&cancelAnimationFrame(v)}}const Hr=xr,Ur=br,Vr=gr,zr=Sr,Yr=yr,Ct=vr,Xr=Er,Kr=(e,t,n)=>{const r=new Map,o={platform:$r,...n},i={...o.platform,_c:r};return pr(e,t,{...o,platform:i})};var Ae=typeof document<"u"?l.useLayoutEffect:l.useEffect;function Le(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Le(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Le(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Gt(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function At(e,t){const n=Gt(e);return Math.round(t*n)/n}function Be(e){const t=l.useRef(e);return Ae(()=>{t.current=e}),t}function qr(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:c}={},transform:s=!0,whileElementsMounted:u,open:a}=e,[m,d]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=l.useState(r);Le(p,r)||h(r);const[v,f]=l.useState(null),[g,y]=l.useState(null),w=l.useCallback(A=>{A!==C.current&&(C.current=A,f(A))},[]),b=l.useCallback(A=>{A!==S.current&&(S.current=A,y(A))},[]),x=i||v,E=c||g,C=l.useRef(null),S=l.useRef(null),P=l.useRef(m),T=u!=null,O=Be(u),_=Be(o),F=Be(a),M=l.useCallback(()=>{if(!C.current||!S.current)return;const A={placement:t,strategy:n,middleware:p};_.current&&(A.platform=_.current),Kr(C.current,S.current,A).then(k=>{const H={...k,isPositioned:F.current!==!1};N.current&&!Le(P.current,H)&&(P.current=H,Ft.flushSync(()=>{d(H)}))})},[p,t,n,_,F]);Ae(()=>{a===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,d(A=>({...A,isPositioned:!1})))},[a]);const N=l.useRef(!1);Ae(()=>(N.current=!0,()=>{N.current=!1}),[]),Ae(()=>{if(x&&(C.current=x),E&&(S.current=E),x&&E){if(O.current)return O.current(x,E,M);M()}},[x,E,M,O,T]);const $=l.useMemo(()=>({reference:C,floating:S,setReference:w,setFloating:b}),[w,b]),L=l.useMemo(()=>({reference:x,floating:E}),[x,E]),D=l.useMemo(()=>{const A={position:n,left:0,top:0};if(!L.floating)return A;const k=At(L.floating,m.x),H=At(L.floating,m.y);return s?{...A,transform:"translate("+k+"px, "+H+"px)",...Gt(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:k,top:H}},[n,s,L.floating,m.x,m.y]);return l.useMemo(()=>({...m,update:M,refs:$,elements:L,floatingStyles:D}),[m,M,$,L,D])}const Zr=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Ct({element:r.current,padding:o}).fn(n):{}:r?Ct({element:r,padding:o}).fn(n):{}}}},Gr=(e,t)=>({...Hr(e),options:[e,t]}),Qr=(e,t)=>({...Ur(e),options:[e,t]}),Jr=(e,t)=>({...Xr(e),options:[e,t]}),eo=(e,t)=>({...Vr(e),options:[e,t]}),to=(e,t)=>({...zr(e),options:[e,t]}),no=(e,t)=>({...Yr(e),options:[e,t]}),ro=(e,t)=>({...Zr(e),options:[e,t]});var oo="Arrow",Qt=l.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return R.jsx(oe.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:R.jsx("polygon",{points:"0,0 30,0 15,10"})})});Qt.displayName=oo;var io=Qt;function so(e,t=[]){let n=[];function r(i,c){const s=l.createContext(c),u=n.length;n=[...n,c];function a(d){const{scope:p,children:h,...v}=d,f=(p==null?void 0:p[e][u])||s,g=l.useMemo(()=>v,Object.values(v));return R.jsx(f.Provider,{value:g,children:h})}function m(d,p){const h=(p==null?void 0:p[e][u])||s,v=l.useContext(h);if(v)return v;if(c!==void 0)return c;throw new Error(`\`${d}\` must be used within \`${i}\``)}return a.displayName=i+"Provider",[a,m]}const o=()=>{const i=n.map(c=>l.createContext(c));return function(s){const u=(s==null?void 0:s[e])||i;return l.useMemo(()=>({[`__scope${e}`]:{...s,[e]:u}}),[s,u])}};return o.scopeName=e,[r,co(o,...t)]}function co(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const c=r.reduce((s,{useScope:u,scopeName:a})=>{const d=u(i)[`__scope${a}`];return{...s,...d}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}function ao(e){const[t,n]=l.useState(void 0);return ne(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let c,s;if("borderBoxSize"in i){const u=i.borderBoxSize,a=Array.isArray(u)?u[0]:u;c=a.inlineSize,s=a.blockSize}else c=e.offsetWidth,s=e.offsetHeight;n({width:c,height:s})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var ct="Popper",[Jt,yi]=so(ct),[lo,en]=Jt(ct),tn=e=>{const{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return R.jsx(lo,{scope:t,anchor:r,onAnchorChange:o,children:n})};tn.displayName=ct;var nn="PopperAnchor",rn=l.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=en(nn,n),c=l.useRef(null),s=q(t,c);return l.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||c.current)}),r?null:R.jsx(oe.div,{...o,ref:s})});rn.displayName=nn;var at="PopperContent",[uo,fo]=Jt(at),on=l.forwardRef((e,t)=>{var lt,ut,ft,dt,mt,ht;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:c=0,arrowPadding:s=0,avoidCollisions:u=!0,collisionBoundary:a=[],collisionPadding:m=0,sticky:d="partial",hideWhenDetached:p=!1,updatePositionStrategy:h="optimized",onPlaced:v,...f}=e,g=en(at,n),[y,w]=l.useState(null),b=q(t,me=>w(me)),[x,E]=l.useState(null),C=ao(x),S=(C==null?void 0:C.width)??0,P=(C==null?void 0:C.height)??0,T=r+(i!=="center"?"-"+i:""),O=typeof m=="number"?m:{top:0,right:0,bottom:0,left:0,...m},_=Array.isArray(a)?a:[a],F=_.length>0,M={padding:O,boundary:_.filter(ho),altBoundary:F},{refs:N,floatingStyles:$,placement:L,isPositioned:D,middlewareData:A}=qr({strategy:"fixed",placement:T,whileElementsMounted:(...me)=>jr(...me,{animationFrame:h==="always"}),elements:{reference:g.anchor},middleware:[Gr({mainAxis:o+P,alignmentAxis:c}),u&&Qr({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?Jr():void 0,...M}),u&&eo({...M}),to({...M,apply:({elements:me,rects:pt,availableWidth:Sn,availableHeight:Cn})=>{const{width:An,height:Pn}=pt.reference,ye=me.floating.style;ye.setProperty("--radix-popper-available-width",`${Sn}px`),ye.setProperty("--radix-popper-available-height",`${Cn}px`),ye.setProperty("--radix-popper-anchor-width",`${An}px`),ye.setProperty("--radix-popper-anchor-height",`${Pn}px`)}}),x&&ro({element:x,padding:s}),po({arrowWidth:S,arrowHeight:P}),p&&no({strategy:"referenceHidden",...M})]}),[k,H]=an(L),ge=Z(v);ne(()=>{D&&(ge==null||ge())},[D,ge]);const yn=(lt=A.arrow)==null?void 0:lt.x,wn=(ut=A.arrow)==null?void 0:ut.y,xn=((ft=A.arrow)==null?void 0:ft.centerOffset)!==0,[bn,En]=l.useState();return ne(()=>{y&&En(window.getComputedStyle(y).zIndex)},[y]),R.jsx("div",{ref:N.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:D?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:bn,"--radix-popper-transform-origin":[(dt=A.transformOrigin)==null?void 0:dt.x,(mt=A.transformOrigin)==null?void 0:mt.y].join(" "),...((ht=A.hide)==null?void 0:ht.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:R.jsx(uo,{scope:n,placedSide:k,onArrowChange:E,arrowX:yn,arrowY:wn,shouldHideArrow:xn,children:R.jsx(oe.div,{"data-side":k,"data-align":H,...f,ref:b,style:{...f.style,animation:D?void 0:"none"}})})})});on.displayName=at;var sn="PopperArrow",mo={top:"bottom",right:"left",bottom:"top",left:"right"},cn=l.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=fo(sn,r),c=mo[i.placedSide];return R.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:R.jsx(io,{...o,ref:n,style:{...o.style,display:"block"}})})});cn.displayName=sn;function ho(e){return e!==null}var po=e=>({name:"transformOrigin",options:e,fn(t){var g,y,w;const{placement:n,rects:r,middlewareData:o}=t,c=((g=o.arrow)==null?void 0:g.centerOffset)!==0,s=c?0:e.arrowWidth,u=c?0:e.arrowHeight,[a,m]=an(n),d={start:"0%",center:"50%",end:"100%"}[m],p=(((y=o.arrow)==null?void 0:y.x)??0)+s/2,h=(((w=o.arrow)==null?void 0:w.y)??0)+u/2;let v="",f="";return a==="bottom"?(v=c?d:`${p}px`,f=`${-u}px`):a==="top"?(v=c?d:`${p}px`,f=`${r.floating.height+u}px`):a==="right"?(v=`${-u}px`,f=c?d:`${h}px`):a==="left"&&(v=`${r.floating.width+u}px`,f=c?d:`${h}px`),{data:{x:v,y:f}}}});function an(e){const[t,n="center"]=e.split("-");return[t,n]}var wi=tn,xi=rn,bi=on,Ei=cn,je=0;function Si(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Pt()),document.body.insertAdjacentElement("beforeend",e[1]??Pt()),je++,()=>{je===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),je--}},[])}function Pt(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var He="focusScope.autoFocusOnMount",Ue="focusScope.autoFocusOnUnmount",Rt={bubbles:!1,cancelable:!0},vo="FocusScope",go=l.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...c}=e,[s,u]=l.useState(null),a=Z(o),m=Z(i),d=l.useRef(null),p=q(t,f=>u(f)),h=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let f=function(b){if(h.paused||!s)return;const x=b.target;s.contains(x)?d.current=x:K(d.current,{select:!0})},g=function(b){if(h.paused||!s)return;const x=b.relatedTarget;x!==null&&(s.contains(x)||K(d.current,{select:!0}))},y=function(b){if(document.activeElement===document.body)for(const E of b)E.removedNodes.length>0&&K(s)};document.addEventListener("focusin",f),document.addEventListener("focusout",g);const w=new MutationObserver(y);return s&&w.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",f),document.removeEventListener("focusout",g),w.disconnect()}}},[r,s,h.paused]),l.useEffect(()=>{if(s){Nt.add(h);const f=document.activeElement;if(!s.contains(f)){const y=new CustomEvent(He,Rt);s.addEventListener(He,a),s.dispatchEvent(y),y.defaultPrevented||(yo(So(ln(s)),{select:!0}),document.activeElement===f&&K(s))}return()=>{s.removeEventListener(He,a),setTimeout(()=>{const y=new CustomEvent(Ue,Rt);s.addEventListener(Ue,m),s.dispatchEvent(y),y.defaultPrevented||K(f??document.body,{select:!0}),s.removeEventListener(Ue,m),Nt.remove(h)},0)}}},[s,a,m,h]);const v=l.useCallback(f=>{if(!n&&!r||h.paused)return;const g=f.key==="Tab"&&!f.altKey&&!f.ctrlKey&&!f.metaKey,y=document.activeElement;if(g&&y){const w=f.currentTarget,[b,x]=wo(w);b&&x?!f.shiftKey&&y===x?(f.preventDefault(),n&&K(b,{select:!0})):f.shiftKey&&y===b&&(f.preventDefault(),n&&K(x,{select:!0})):y===w&&f.preventDefault()}},[n,r,h.paused]);return R.jsx(oe.div,{tabIndex:-1,...c,ref:p,onKeyDown:v})});go.displayName=vo;function yo(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(K(r,{select:t}),document.activeElement!==n)return}function wo(e){const t=ln(e),n=Ot(t,e),r=Ot(t.reverse(),e);return[n,r]}function ln(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ot(e,t){for(const n of e)if(!xo(n,{upTo:t}))return n}function xo(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function bo(e){return e instanceof HTMLInputElement&&"select"in e}function K(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&bo(e)&&t&&e.select()}}var Nt=Eo();function Eo(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Tt(e,t),e.unshift(t)},remove(t){var n;e=Tt(e,t),(n=e[0])==null||n.resume()}}}function Tt(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function So(e){return e.filter(t=>t.tagName!=="A")}var Co=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},ie=new WeakMap,be=new WeakMap,Ee={},Ve=0,un=function(e){return e&&(e.host||un(e.parentNode))},Ao=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=un(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Po=function(e,t,n,r){var o=Ao(t,Array.isArray(e)?e:[e]);Ee[n]||(Ee[n]=new WeakMap);var i=Ee[n],c=[],s=new Set,u=new Set(o),a=function(d){!d||s.has(d)||(s.add(d),a(d.parentNode))};o.forEach(a);var m=function(d){!d||u.has(d)||Array.prototype.forEach.call(d.children,function(p){if(s.has(p))m(p);else try{var h=p.getAttribute(r),v=h!==null&&h!=="false",f=(ie.get(p)||0)+1,g=(i.get(p)||0)+1;ie.set(p,f),i.set(p,g),c.push(p),f===1&&v&&be.set(p,!0),g===1&&p.setAttribute(n,"true"),v||p.setAttribute(r,"true")}catch(y){console.error("aria-hidden: cannot operate on ",p,y)}})};return m(t),s.clear(),Ve++,function(){c.forEach(function(d){var p=ie.get(d)-1,h=i.get(d)-1;ie.set(d,p),i.set(d,h),p||(be.has(d)||d.removeAttribute(r),be.delete(d)),h||d.removeAttribute(n)}),Ve--,Ve||(ie=new WeakMap,ie=new WeakMap,be=new WeakMap,Ee={})}},Ci=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Co(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Po(r,o,n,"aria-hidden")):function(){return null}},U=function(){return U=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},U.apply(this,arguments)};function fn(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Ro(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var Pe="right-scroll-bar-position",Re="width-before-scroll-bar",Oo="with-scroll-bars-hidden",No="--removed-body-scroll-bar-size";function ze(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function To(e,t){var n=l.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Mo=typeof window<"u"?l.useLayoutEffect:l.useEffect,Mt=new WeakMap;function Lo(e,t){var n=To(null,function(r){return e.forEach(function(o){return ze(o,r)})});return Mo(function(){var r=Mt.get(n);if(r){var o=new Set(r),i=new Set(e),c=n.current;o.forEach(function(s){i.has(s)||ze(s,null)}),i.forEach(function(s){o.has(s)||ze(s,c)})}Mt.set(n,e)},[e]),n}function Do(e){return e}function ko(e,t){t===void 0&&(t=Do);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var c=t(i,r);return n.push(c),function(){n=n.filter(function(s){return s!==c})}},assignSyncMedium:function(i){for(r=!0;n.length;){var c=n;n=[],c.forEach(i)}n={push:function(s){return i(s)},filter:function(){return n}}},assignMedium:function(i){r=!0;var c=[];if(n.length){var s=n;n=[],s.forEach(i),c=n}var u=function(){var m=c;c=[],m.forEach(i)},a=function(){return Promise.resolve().then(u)};a(),n={push:function(m){c.push(m),a()},filter:function(m){return c=c.filter(m),n}}}};return o}function _o(e){e===void 0&&(e={});var t=ko(null);return t.options=U({async:!0,ssr:!1},e),t}var dn=function(e){var t=e.sideCar,n=fn(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return l.createElement(r,U({},n))};dn.isSideCarExport=!0;function Fo(e,t){return e.useMedium(t),dn}var mn=_o(),Ye=function(){},Ie=l.forwardRef(function(e,t){var n=l.useRef(null),r=l.useState({onScrollCapture:Ye,onWheelCapture:Ye,onTouchMoveCapture:Ye}),o=r[0],i=r[1],c=e.forwardProps,s=e.children,u=e.className,a=e.removeScrollBar,m=e.enabled,d=e.shards,p=e.sideCar,h=e.noIsolation,v=e.inert,f=e.allowPinchZoom,g=e.as,y=g===void 0?"div":g,w=e.gapMode,b=fn(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=p,E=Lo([n,t]),C=U(U({},b),o);return l.createElement(l.Fragment,null,m&&l.createElement(x,{sideCar:mn,removeScrollBar:a,shards:d,noIsolation:h,inert:v,setCallbacks:i,allowPinchZoom:!!f,lockRef:n,gapMode:w}),c?l.cloneElement(l.Children.only(s),U(U({},C),{ref:E})):l.createElement(y,U({},C,{className:u,ref:E}),s))});Ie.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ie.classNames={fullWidth:Re,zeroRight:Pe};var Io=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Wo(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Io();return t&&e.setAttribute("nonce",t),e}function $o(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Bo(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var jo=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Wo())&&($o(t,n),Bo(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Ho=function(){var e=jo();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},hn=function(){var e=Ho(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Uo={left:0,top:0,right:0,gap:0},Xe=function(e){return parseInt(e||"",10)||0},Vo=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Xe(n),Xe(r),Xe(o)]},zo=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Uo;var t=Vo(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Yo=hn(),le="data-scroll-locked",Xo=function(e,t,n,r){var o=e.left,i=e.top,c=e.right,s=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Oo,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(s,"px ").concat(r,`;
  }
  body[`).concat(le,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(s,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Pe,` {
    right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat(Re,` {
    margin-right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat(Pe," .").concat(Pe,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Re," .").concat(Re,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(le,`] {
    `).concat(No,": ").concat(s,`px;
  }
`)},Lt=function(){var e=parseInt(document.body.getAttribute(le)||"0",10);return isFinite(e)?e:0},Ko=function(){l.useEffect(function(){return document.body.setAttribute(le,(Lt()+1).toString()),function(){var e=Lt()-1;e<=0?document.body.removeAttribute(le):document.body.setAttribute(le,e.toString())}},[])},qo=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Ko();var i=l.useMemo(function(){return zo(o)},[o]);return l.createElement(Yo,{styles:Xo(i,!t,o,n?"":"!important")})},et=!1;if(typeof window<"u")try{var Se=Object.defineProperty({},"passive",{get:function(){return et=!0,!0}});window.addEventListener("test",Se,Se),window.removeEventListener("test",Se,Se)}catch{et=!1}var se=et?{passive:!1}:!1,Zo=function(e){return e.tagName==="TEXTAREA"},pn=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Zo(e)&&n[t]==="visible")},Go=function(e){return pn(e,"overflowY")},Qo=function(e){return pn(e,"overflowX")},Dt=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=vn(e,r);if(o){var i=gn(e,r),c=i[1],s=i[2];if(c>s)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Jo=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},ei=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},vn=function(e,t){return e==="v"?Go(t):Qo(t)},gn=function(e,t){return e==="v"?Jo(t):ei(t)},ti=function(e,t){return e==="h"&&t==="rtl"?-1:1},ni=function(e,t,n,r,o){var i=ti(e,window.getComputedStyle(t).direction),c=i*r,s=n.target,u=t.contains(s),a=!1,m=c>0,d=0,p=0;do{var h=gn(e,s),v=h[0],f=h[1],g=h[2],y=f-g-i*v;(v||y)&&vn(e,s)&&(d+=y,p+=v),s instanceof ShadowRoot?s=s.host:s=s.parentNode}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return(m&&(Math.abs(d)<1||!o)||!m&&(Math.abs(p)<1||!o))&&(a=!0),a},Ce=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},kt=function(e){return[e.deltaX,e.deltaY]},_t=function(e){return e&&"current"in e?e.current:e},ri=function(e,t){return e[0]===t[0]&&e[1]===t[1]},oi=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},ii=0,ce=[];function si(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(ii++)[0],i=l.useState(hn)[0],c=l.useRef(e);l.useEffect(function(){c.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var f=Ro([e.lockRef.current],(e.shards||[]).map(_t),!0).filter(Boolean);return f.forEach(function(g){return g.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),f.forEach(function(g){return g.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(f,g){if("touches"in f&&f.touches.length===2||f.type==="wheel"&&f.ctrlKey)return!c.current.allowPinchZoom;var y=Ce(f),w=n.current,b="deltaX"in f?f.deltaX:w[0]-y[0],x="deltaY"in f?f.deltaY:w[1]-y[1],E,C=f.target,S=Math.abs(b)>Math.abs(x)?"h":"v";if("touches"in f&&S==="h"&&C.type==="range")return!1;var P=Dt(S,C);if(!P)return!0;if(P?E=S:(E=S==="v"?"h":"v",P=Dt(S,C)),!P)return!1;if(!r.current&&"changedTouches"in f&&(b||x)&&(r.current=E),!E)return!0;var T=r.current||E;return ni(T,g,f,T==="h"?b:x,!0)},[]),u=l.useCallback(function(f){var g=f;if(!(!ce.length||ce[ce.length-1]!==i)){var y="deltaY"in g?kt(g):Ce(g),w=t.current.filter(function(E){return E.name===g.type&&(E.target===g.target||g.target===E.shadowParent)&&ri(E.delta,y)})[0];if(w&&w.should){g.cancelable&&g.preventDefault();return}if(!w){var b=(c.current.shards||[]).map(_t).filter(Boolean).filter(function(E){return E.contains(g.target)}),x=b.length>0?s(g,b[0]):!c.current.noIsolation;x&&g.cancelable&&g.preventDefault()}}},[]),a=l.useCallback(function(f,g,y,w){var b={name:f,delta:g,target:y,should:w,shadowParent:ci(y)};t.current.push(b),setTimeout(function(){t.current=t.current.filter(function(x){return x!==b})},1)},[]),m=l.useCallback(function(f){n.current=Ce(f),r.current=void 0},[]),d=l.useCallback(function(f){a(f.type,kt(f),f.target,s(f,e.lockRef.current))},[]),p=l.useCallback(function(f){a(f.type,Ce(f),f.target,s(f,e.lockRef.current))},[]);l.useEffect(function(){return ce.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,se),document.addEventListener("touchmove",u,se),document.addEventListener("touchstart",m,se),function(){ce=ce.filter(function(f){return f!==i}),document.removeEventListener("wheel",u,se),document.removeEventListener("touchmove",u,se),document.removeEventListener("touchstart",m,se)}},[]);var h=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(i,{styles:oi(o)}):null,h?l.createElement(qo,{gapMode:e.gapMode}):null)}function ci(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ai=Fo(mn,si);var li=l.forwardRef(function(e,t){return l.createElement(Ie,U({},e,{ref:t,sideCar:ai}))});li.classNames=Ie.classNames;var ui=l.createContext(void 0);function Ai(e){const t=l.useContext(ui);return e||t||"ltr"}export{xi as A,pi as B,bi as C,jt as D,go as F,oe as P,hi as R,Wn as S,mi as a,vi as b,di as c,tr as d,Z as e,We as f,Jn as g,ne as h,Vn as i,R as j,yi as k,Ei as l,Ci as m,li as n,Oe as o,Si as p,wi as q,gi as r,Ai as s,ao as t,q as u};
