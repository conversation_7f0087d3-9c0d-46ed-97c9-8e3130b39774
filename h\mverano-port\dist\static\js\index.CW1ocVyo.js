var qr=e=>{throw TypeError(e)};var tr=(e,t,r)=>t.has(e)||qr("Cannot "+r);var v=(e,t,r)=>(tr(e,t,"read from private field"),r?r.call(e):t.get(e)),_=(e,t,r)=>t.has(e)?qr("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),R=(e,t,r,o)=>(tr(e,t,"write to private field"),o?o.call(e,r):t.set(e,r),r),te=(e,t,r)=>(tr(e,t,"access private method"),r);var Nt=(e,t,r,o)=>({set _(n){R(e,t,n,r)},get _(){return v(e,t,o)}});import{j as s,P as se,c as Ns,a as _t,u as me,B as Sn,b as jr,d as Bt,e as It,f as $,R as Tn,g as Ss,h as En,i as kn,k as Vt,A as Cr,D as Ts,C as Es,S as Pn,l as ks,m as Mn,n as An,o as Ps,p as Rn,F as zn,q as In,r as Dn,s as On,t as Fn}from"./ui.Dv2fOKTw.js";import{b as Ms,r as u,a as C,G as Ln}from"./vendor.B23LvuPX.js";import{u as _n,B as Bn,R as Vn,a as Ur}from"./router.ORQh2j4p.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const a of n)if(a.type==="childList")for(const i of a.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&o(i)}).observe(document,{childList:!0,subtree:!0});function r(n){const a={};return n.integrity&&(a.integrity=n.integrity),n.referrerPolicy&&(a.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?a.credentials="include":n.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function o(n){if(n.ep)return;n.ep=!0;const a=r(n);fetch(n.href,a)}})();var As,Yr=Ms;As=Yr.createRoot,Yr.hydrateRoot;const Hn=1,Wn=1e6;let rr=0;function $n(){return rr=(rr+1)%Number.MAX_SAFE_INTEGER,rr.toString()}const sr=new Map,Kr=e=>{if(sr.has(e))return;const t=setTimeout(()=>{sr.delete(e),mt({type:"REMOVE_TOAST",toastId:e})},Wn);sr.set(e,t)},Gn=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Hn)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(r=>r.id===t.toast.id?{...r,...t.toast}:r)};case"DISMISS_TOAST":{const{toastId:r}=t;return r?Kr(r):e.toasts.forEach(o=>{Kr(o.id)}),{...e,toasts:e.toasts.map(o=>o.id===r||r===void 0?{...o,open:!1}:o)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(r=>r.id!==t.toastId)}}},At=[];let Rt={toasts:[]};function mt(e){Rt=Gn(Rt,e),At.forEach(t=>{t(Rt)})}function qn({...e}){const t=$n(),r=n=>mt({type:"UPDATE_TOAST",toast:{...n,id:t}}),o=()=>mt({type:"DISMISS_TOAST",toastId:t});return mt({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:n=>{n||o()}}}),{id:t,dismiss:o,update:r}}function Un(){const[e,t]=u.useState(Rt);return u.useEffect(()=>(At.push(t),()=>{const r=At.indexOf(t);r>-1&&At.splice(r,1)}),[e]),{...e,toast:qn,dismiss:r=>mt({type:"DISMISS_TOAST",toastId:r})}}var Yn="VisuallyHidden",Ht=u.forwardRef((e,t)=>s.jsx(se.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Ht.displayName=Yn;var Kn=Ht,Nr="ToastProvider",[Sr,Qn,Xn]=Ns("Toast"),[Rs,i1]=_t("Toast",[Xn]),[Jn,Wt]=Rs(Nr),zs=e=>{const{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:n="right",swipeThreshold:a=50,children:i}=e,[l,d]=u.useState(null),[h,g]=u.useState(0),p=u.useRef(!1),c=u.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${Nr}\`. Expected non-empty \`string\`.`),s.jsx(Sr.Provider,{scope:t,children:s.jsx(Jn,{scope:t,label:r,duration:o,swipeDirection:n,swipeThreshold:a,toastCount:h,viewport:l,onViewportChange:d,onToastAdd:u.useCallback(()=>g(b=>b+1),[]),onToastRemove:u.useCallback(()=>g(b=>b-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:c,children:i})})};zs.displayName=Nr;var Is="ToastViewport",Zn=["F8"],hr="toast.viewportPause",mr="toast.viewportResume",Ds=u.forwardRef((e,t)=>{const{__scopeToast:r,hotkey:o=Zn,label:n="Notifications ({hotkey})",...a}=e,i=Wt(Is,r),l=Qn(r),d=u.useRef(null),h=u.useRef(null),g=u.useRef(null),p=u.useRef(null),c=me(t,p,i.onViewportChange),b=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=i.toastCount>0;u.useEffect(()=>{const f=y=>{var w;o.length!==0&&o.every(j=>y[j]||y.code===j)&&((w=p.current)==null||w.focus())};return document.addEventListener("keydown",f),()=>document.removeEventListener("keydown",f)},[o]),u.useEffect(()=>{const f=d.current,y=p.current;if(x&&f&&y){const N=()=>{if(!i.isClosePausedRef.current){const P=new CustomEvent(hr);y.dispatchEvent(P),i.isClosePausedRef.current=!0}},w=()=>{if(i.isClosePausedRef.current){const P=new CustomEvent(mr);y.dispatchEvent(P),i.isClosePausedRef.current=!1}},j=P=>{!f.contains(P.relatedTarget)&&w()},S=()=>{f.contains(document.activeElement)||w()};return f.addEventListener("focusin",N),f.addEventListener("focusout",j),f.addEventListener("pointermove",N),f.addEventListener("pointerleave",S),window.addEventListener("blur",N),window.addEventListener("focus",w),()=>{f.removeEventListener("focusin",N),f.removeEventListener("focusout",j),f.removeEventListener("pointermove",N),f.removeEventListener("pointerleave",S),window.removeEventListener("blur",N),window.removeEventListener("focus",w)}}},[x,i.isClosePausedRef]);const m=u.useCallback(({tabbingDirection:f})=>{const N=l().map(w=>{const j=w.ref.current,S=[j,...ha(j)];return f==="forwards"?S:S.reverse()});return(f==="forwards"?N.reverse():N).flat()},[l]);return u.useEffect(()=>{const f=p.current;if(f){const y=N=>{var S,P,L;const w=N.altKey||N.ctrlKey||N.metaKey;if(N.key==="Tab"&&!w){const I=document.activeElement,W=N.shiftKey;if(N.target===f&&W){(S=h.current)==null||S.focus();return}const k=m({tabbingDirection:W?"backwards":"forwards"}),Z=k.findIndex(T=>T===I);or(k.slice(Z+1))?N.preventDefault():W?(P=h.current)==null||P.focus():(L=g.current)==null||L.focus()}};return f.addEventListener("keydown",y),()=>f.removeEventListener("keydown",y)}},[l,m]),s.jsxs(Sn,{ref:d,role:"region","aria-label":n.replace("{hotkey}",b),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&s.jsx(fr,{ref:h,onFocusFromOutsideViewport:()=>{const f=m({tabbingDirection:"forwards"});or(f)}}),s.jsx(Sr.Slot,{scope:r,children:s.jsx(se.ol,{tabIndex:-1,...a,ref:c})}),x&&s.jsx(fr,{ref:g,onFocusFromOutsideViewport:()=>{const f=m({tabbingDirection:"backwards"});or(f)}})]})});Ds.displayName=Is;var Os="ToastFocusProxy",fr=u.forwardRef((e,t)=>{const{__scopeToast:r,onFocusFromOutsideViewport:o,...n}=e,a=Wt(Os,r);return s.jsx(Ht,{"aria-hidden":!0,tabIndex:0,...n,ref:t,style:{position:"fixed"},onFocus:i=>{var h;const l=i.relatedTarget;!((h=a.viewport)!=null&&h.contains(l))&&o()}})});fr.displayName=Os;var $t="Toast",ea="toast.swipeStart",ta="toast.swipeMove",ra="toast.swipeCancel",sa="toast.swipeEnd",Fs=u.forwardRef((e,t)=>{const{forceMount:r,open:o,defaultOpen:n,onOpenChange:a,...i}=e,[l=!0,d]=jr({prop:o,defaultProp:n,onChange:a});return s.jsx(Bt,{present:r||l,children:s.jsx(aa,{open:l,...i,ref:t,onClose:()=>d(!1),onPause:It(e.onPause),onResume:It(e.onResume),onSwipeStart:$(e.onSwipeStart,h=>{h.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:$(e.onSwipeMove,h=>{const{x:g,y:p}=h.detail.delta;h.currentTarget.setAttribute("data-swipe","move"),h.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${g}px`),h.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${p}px`)}),onSwipeCancel:$(e.onSwipeCancel,h=>{h.currentTarget.setAttribute("data-swipe","cancel"),h.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),h.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),h.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),h.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:$(e.onSwipeEnd,h=>{const{x:g,y:p}=h.detail.delta;h.currentTarget.setAttribute("data-swipe","end"),h.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),h.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),h.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${g}px`),h.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${p}px`),d(!1)})})})});Fs.displayName=$t;var[oa,na]=Rs($t,{onClose(){}}),aa=u.forwardRef((e,t)=>{const{__scopeToast:r,type:o="foreground",duration:n,open:a,onClose:i,onEscapeKeyDown:l,onPause:d,onResume:h,onSwipeStart:g,onSwipeMove:p,onSwipeCancel:c,onSwipeEnd:b,...x}=e,m=Wt($t,r),[f,y]=u.useState(null),N=me(t,T=>y(T)),w=u.useRef(null),j=u.useRef(null),S=n||m.duration,P=u.useRef(0),L=u.useRef(S),I=u.useRef(0),{onToastAdd:W,onToastRemove:Q}=m,O=It(()=>{var G;(f==null?void 0:f.contains(document.activeElement))&&((G=m.viewport)==null||G.focus()),i()}),k=u.useCallback(T=>{!T||T===1/0||(window.clearTimeout(I.current),P.current=new Date().getTime(),I.current=window.setTimeout(O,T))},[O]);u.useEffect(()=>{const T=m.viewport;if(T){const G=()=>{k(L.current),h==null||h()},B=()=>{const J=new Date().getTime()-P.current;L.current=L.current-J,window.clearTimeout(I.current),d==null||d()};return T.addEventListener(hr,B),T.addEventListener(mr,G),()=>{T.removeEventListener(hr,B),T.removeEventListener(mr,G)}}},[m.viewport,S,d,h,k]),u.useEffect(()=>{a&&!m.isClosePausedRef.current&&k(S)},[a,S,m.isClosePausedRef,k]),u.useEffect(()=>(W(),()=>Q()),[W,Q]);const Z=u.useMemo(()=>f?$s(f):null,[f]);return m.viewport?s.jsxs(s.Fragment,{children:[Z&&s.jsx(ia,{__scopeToast:r,role:"status","aria-live":o==="foreground"?"assertive":"polite","aria-atomic":!0,children:Z}),s.jsx(oa,{scope:r,onClose:O,children:Ms.createPortal(s.jsx(Sr.ItemSlot,{scope:r,children:s.jsx(Tn,{asChild:!0,onEscapeKeyDown:$(l,()=>{m.isFocusedToastEscapeKeyDownRef.current||O(),m.isFocusedToastEscapeKeyDownRef.current=!1}),children:s.jsx(se.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":a?"open":"closed","data-swipe-direction":m.swipeDirection,...x,ref:N,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:$(e.onKeyDown,T=>{T.key==="Escape"&&(l==null||l(T.nativeEvent),T.nativeEvent.defaultPrevented||(m.isFocusedToastEscapeKeyDownRef.current=!0,O()))}),onPointerDown:$(e.onPointerDown,T=>{T.button===0&&(w.current={x:T.clientX,y:T.clientY})}),onPointerMove:$(e.onPointerMove,T=>{if(!w.current)return;const G=T.clientX-w.current.x,B=T.clientY-w.current.y,J=!!j.current,E=["left","right"].includes(m.swipeDirection),U=["left","up"].includes(m.swipeDirection)?Math.min:Math.max,je=E?U(0,G):0,z=E?0:U(0,B),V=T.pointerType==="touch"?10:2,H={x:je,y:z},ae={originalEvent:T,delta:H};J?(j.current=H,St(ta,p,ae,{discrete:!1})):Qr(H,m.swipeDirection,V)?(j.current=H,St(ea,g,ae,{discrete:!1}),T.target.setPointerCapture(T.pointerId)):(Math.abs(G)>V||Math.abs(B)>V)&&(w.current=null)}),onPointerUp:$(e.onPointerUp,T=>{const G=j.current,B=T.target;if(B.hasPointerCapture(T.pointerId)&&B.releasePointerCapture(T.pointerId),j.current=null,w.current=null,G){const J=T.currentTarget,E={originalEvent:T,delta:G};Qr(G,m.swipeDirection,m.swipeThreshold)?St(sa,b,E,{discrete:!0}):St(ra,c,E,{discrete:!0}),J.addEventListener("click",U=>U.preventDefault(),{once:!0})}})})})}),m.viewport)})]}):null}),ia=e=>{const{__scopeToast:t,children:r,...o}=e,n=Wt($t,t),[a,i]=u.useState(!1),[l,d]=u.useState(!1);return da(()=>i(!0)),u.useEffect(()=>{const h=window.setTimeout(()=>d(!0),1e3);return()=>window.clearTimeout(h)},[]),l?null:s.jsx(Ss,{asChild:!0,children:s.jsx(Ht,{...o,children:a&&s.jsxs(s.Fragment,{children:[n.label," ",r]})})})},la="ToastTitle",Ls=u.forwardRef((e,t)=>{const{__scopeToast:r,...o}=e;return s.jsx(se.div,{...o,ref:t})});Ls.displayName=la;var ca="ToastDescription",_s=u.forwardRef((e,t)=>{const{__scopeToast:r,...o}=e;return s.jsx(se.div,{...o,ref:t})});_s.displayName=ca;var Bs="ToastAction",Vs=u.forwardRef((e,t)=>{const{altText:r,...o}=e;return r.trim()?s.jsx(Ws,{altText:r,asChild:!0,children:s.jsx(Tr,{...o,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Bs}\`. Expected non-empty \`string\`.`),null)});Vs.displayName=Bs;var Hs="ToastClose",Tr=u.forwardRef((e,t)=>{const{__scopeToast:r,...o}=e,n=na(Hs,r);return s.jsx(Ws,{asChild:!0,children:s.jsx(se.button,{type:"button",...o,ref:t,onClick:$(e.onClick,n.onClose)})})});Tr.displayName=Hs;var Ws=u.forwardRef((e,t)=>{const{__scopeToast:r,altText:o,...n}=e;return s.jsx(se.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":o||void 0,...n,ref:t})});function $s(e){const t=[];return Array.from(e.childNodes).forEach(o=>{if(o.nodeType===o.TEXT_NODE&&o.textContent&&t.push(o.textContent),ua(o)){const n=o.ariaHidden||o.hidden||o.style.display==="none",a=o.dataset.radixToastAnnounceExclude==="";if(!n)if(a){const i=o.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...$s(o))}}),t}function St(e,t,r,{discrete:o}){const n=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&n.addEventListener(e,t,{once:!0}),o?kn(n,a):n.dispatchEvent(a)}var Qr=(e,t,r=0)=>{const o=Math.abs(e.x),n=Math.abs(e.y),a=o>n;return t==="left"||t==="right"?a&&o>r:!a&&n>r};function da(e=()=>{}){const t=It(e);En(()=>{let r=0,o=0;return r=window.requestAnimationFrame(()=>o=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(r),window.cancelAnimationFrame(o)}},[t])}function ua(e){return e.nodeType===e.ELEMENT_NODE}function ha(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const n=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||n?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function or(e){const t=document.activeElement;return e.some(r=>r===t?!0:(r.focus(),document.activeElement!==t))}var ma=zs,Gs=Ds,qs=Fs,Us=Ls,Ys=_s,Ks=Vs,Qs=Tr;function Xs(e){var t,r,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var n=e.length;for(t=0;t<n;t++)e[t]&&(r=Xs(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function Js(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=Xs(e))&&(o&&(o+=" "),o+=t);return o}const Xr=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Jr=Js,Zs=(e,t)=>r=>{var o;if((t==null?void 0:t.variants)==null)return Jr(e,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:n,defaultVariants:a}=t,i=Object.keys(n).map(h=>{const g=r==null?void 0:r[h],p=a==null?void 0:a[h];if(g===null)return null;const c=Xr(g)||Xr(p);return n[h][c]}),l=r&&Object.entries(r).reduce((h,g)=>{let[p,c]=g;return c===void 0||(h[p]=c),h},{}),d=t==null||(o=t.compoundVariants)===null||o===void 0?void 0:o.reduce((h,g)=>{let{class:p,className:c,...b}=g;return Object.entries(b).every(x=>{let[m,f]=x;return Array.isArray(f)?f.includes({...a,...l}[m]):{...a,...l}[m]===f})?[...h,p,c]:h},[]);return Jr(e,i,d,r==null?void 0:r.class,r==null?void 0:r.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fa=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),eo=(...e)=>e.filter((t,r,o)=>!!t&&t.trim()!==""&&o.indexOf(t)===r).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var pa={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ga=u.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:n="",children:a,iconNode:i,...l},d)=>u.createElement("svg",{ref:d,...pa,width:t,height:t,stroke:e,strokeWidth:o?Number(r)*24/Number(t):r,className:eo("lucide",n),...l},[...i.map(([h,g])=>u.createElement(h,g)),...Array.isArray(a)?a:[a]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=(e,t)=>{const r=u.forwardRef(({className:o,...n},a)=>u.createElement(ga,{ref:a,iconNode:t,className:eo(`lucide-${fa(e)}`,o),...n}));return r.displayName=`${e}`,r};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xa=D("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ba=D("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const va=D("CircleStop",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{x:"9",y:"9",width:"6",height:"6",rx:"1",key:"1ssd4o"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ya=D("Computer",[["rect",{width:"14",height:"8",x:"5",y:"2",rx:"2",key:"wc9tft"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",key:"w68u3i"}],["path",{d:"M6 18h2",key:"rwmk9e"}],["path",{d:"M12 18h6",key:"aqd8w3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wa=D("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ja=D("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nr=D("FileArchive",[["path",{d:"M10 12v-1",key:"v7bkov"}],["path",{d:"M10 18v-2",key:"1cjy8d"}],["path",{d:"M10 7V6",key:"dljcrl"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M15.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v16a2 2 0 0 0 .274 1.01",key:"gkbcor"}],["circle",{cx:"10",cy:"20",r:"2",key:"1xzdoj"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $e=D("FileCode",[["path",{d:"M10 12.5 8 15l2 2.5",key:"1tg20x"}],["path",{d:"m14 12.5 2 2.5-2 2.5",key:"yinavb"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z",key:"1mlx9k"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ca=D("FileJson",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 12a1 1 0 0 0-1 1v1a1 1 0 0 1-1 1 1 1 0 0 1 1 1v1a1 1 0 0 0 1 1",key:"1oajmo"}],["path",{d:"M14 18a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1 1 1 0 0 1-1-1v-1a1 1 0 0 0-1-1",key:"mpwhp6"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Na=D("FileQuestion",[["path",{d:"M12 17h.01",key:"p32p05"}],["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z",key:"1mlx9k"}],["path",{d:"M9.1 9a3 3 0 0 1 5.82 1c0 2-3 3-3 3",key:"mhlwft"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sa=D("FileTerminal",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m8 16 2-2-2-2",key:"10vzyd"}],["path",{d:"M12 18h4",key:"1wd2n7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=D("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ta=D("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ft=D("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ea=D("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Er=D("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const to=D("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ro=D("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ka=D("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gt=D("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pa=D("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ma=D("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Aa=D("Network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ra=D("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const za=D("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ia=D("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Da=D("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oa=D("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const so=D("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pt=D("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fa=D("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const La=D("Tv",[["rect",{width:"20",height:"15",x:"2",y:"7",rx:"2",ry:"2",key:"10ag99"}],["polyline",{points:"17 2 12 7 7 2",key:"11pgbg"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _a=D("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ba=D("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=D("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),kr="-",Va=e=>{const t=Wa(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:i=>{const l=i.split(kr);return l[0]===""&&l.length!==1&&l.shift(),oo(l,t)||Ha(i)},getConflictingClassGroupIds:(i,l)=>{const d=r[i]||[];return l&&o[i]?[...d,...o[i]]:d}}},oo=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),n=o?oo(e.slice(1),o):void 0;if(n)return n;if(t.validators.length===0)return;const a=e.join(kr);return(i=t.validators.find(({validator:l})=>l(a)))==null?void 0:i.classGroupId},Zr=/^\[(.+)\]$/,Ha=e=>{if(Zr.test(e)){const t=Zr.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Wa=e=>{const{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return Ga(Object.entries(e.classGroups),r).forEach(([a,i])=>{pr(i,o,a,t)}),o},pr=(e,t,r,o)=>{e.forEach(n=>{if(typeof n=="string"){const a=n===""?t:es(t,n);a.classGroupId=r;return}if(typeof n=="function"){if($a(n)){pr(n(o),t,r,o);return}t.validators.push({validator:n,classGroupId:r});return}Object.entries(n).forEach(([a,i])=>{pr(i,es(t,a),r,o)})})},es=(e,t)=>{let r=e;return t.split(kr).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r},$a=e=>e.isThemeGetter,Ga=(e,t)=>t?e.map(([r,o])=>{const n=o.map(a=>typeof a=="string"?t+a:typeof a=="object"?Object.fromEntries(Object.entries(a).map(([i,l])=>[t+i,l])):a);return[r,n]}):e,qa=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;const n=(a,i)=>{r.set(a,i),t++,t>e&&(t=0,o=r,r=new Map)};return{get(a){let i=r.get(a);if(i!==void 0)return i;if((i=o.get(a))!==void 0)return n(a,i),i},set(a,i){r.has(a)?r.set(a,i):n(a,i)}}},no="!",Ua=e=>{const{separator:t,experimentalParseClassName:r}=e,o=t.length===1,n=t[0],a=t.length,i=l=>{const d=[];let h=0,g=0,p;for(let f=0;f<l.length;f++){let y=l[f];if(h===0){if(y===n&&(o||l.slice(f,f+a)===t)){d.push(l.slice(g,f)),g=f+a;continue}if(y==="/"){p=f;continue}}y==="["?h++:y==="]"&&h--}const c=d.length===0?l:l.substring(g),b=c.startsWith(no),x=b?c.substring(1):c,m=p&&p>g?p-g:void 0;return{modifiers:d,hasImportantModifier:b,baseClassName:x,maybePostfixModifierPosition:m}};return r?l=>r({className:l,parseClassName:i}):i},Ya=e=>{if(e.length<=1)return e;const t=[];let r=[];return e.forEach(o=>{o[0]==="["?(t.push(...r.sort(),o),r=[]):r.push(o)}),t.push(...r.sort()),t},Ka=e=>({cache:qa(e.cacheSize),parseClassName:Ua(e),...Va(e)}),Qa=/\s+/,Xa=(e,t)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n}=t,a=[],i=e.trim().split(Qa);let l="";for(let d=i.length-1;d>=0;d-=1){const h=i[d],{modifiers:g,hasImportantModifier:p,baseClassName:c,maybePostfixModifierPosition:b}=r(h);let x=!!b,m=o(x?c.substring(0,b):c);if(!m){if(!x){l=h+(l.length>0?" "+l:l);continue}if(m=o(c),!m){l=h+(l.length>0?" "+l:l);continue}x=!1}const f=Ya(g).join(":"),y=p?f+no:f,N=y+m;if(a.includes(N))continue;a.push(N);const w=n(m,x);for(let j=0;j<w.length;++j){const S=w[j];a.push(y+S)}l=h+(l.length>0?" "+l:l)}return l};function Ja(){let e=0,t,r,o="";for(;e<arguments.length;)(t=arguments[e++])&&(r=ao(t))&&(o&&(o+=" "),o+=r);return o}const ao=e=>{if(typeof e=="string")return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=ao(e[o]))&&(r&&(r+=" "),r+=t);return r};function Za(e,...t){let r,o,n,a=i;function i(d){const h=t.reduce((g,p)=>p(g),e());return r=Ka(h),o=r.cache.get,n=r.cache.set,a=l,l(d)}function l(d){const h=o(d);if(h)return h;const g=Xa(d,r);return n(d,g),g}return function(){return a(Ja.apply(null,arguments))}}const K=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},io=/^\[(?:([a-z-]+):)?(.+)\]$/i,ei=/^\d+\/\d+$/,ti=new Set(["px","full","screen"]),ri=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,si=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,oi=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ni=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ai=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ye=e=>qe(e)||ti.has(e)||ei.test(e),Ne=e=>ot(e,"length",fi),qe=e=>!!e&&!Number.isNaN(Number(e)),ar=e=>ot(e,"number",qe),ct=e=>!!e&&Number.isInteger(Number(e)),ii=e=>e.endsWith("%")&&qe(e.slice(0,-1)),A=e=>io.test(e),Se=e=>ri.test(e),li=new Set(["length","size","percentage"]),ci=e=>ot(e,li,lo),di=e=>ot(e,"position",lo),ui=new Set(["image","url"]),hi=e=>ot(e,ui,gi),mi=e=>ot(e,"",pi),dt=()=>!0,ot=(e,t,r)=>{const o=io.exec(e);return o?o[1]?typeof t=="string"?o[1]===t:t.has(o[1]):r(o[2]):!1},fi=e=>si.test(e)&&!oi.test(e),lo=()=>!1,pi=e=>ni.test(e),gi=e=>ai.test(e),xi=()=>{const e=K("colors"),t=K("spacing"),r=K("blur"),o=K("brightness"),n=K("borderColor"),a=K("borderRadius"),i=K("borderSpacing"),l=K("borderWidth"),d=K("contrast"),h=K("grayscale"),g=K("hueRotate"),p=K("invert"),c=K("gap"),b=K("gradientColorStops"),x=K("gradientColorStopPositions"),m=K("inset"),f=K("margin"),y=K("opacity"),N=K("padding"),w=K("saturate"),j=K("scale"),S=K("sepia"),P=K("skew"),L=K("space"),I=K("translate"),W=()=>["auto","contain","none"],Q=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto",A,t],k=()=>[A,t],Z=()=>["",ye,Ne],T=()=>["auto",qe,A],G=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],B=()=>["solid","dashed","dotted","double","none"],J=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],E=()=>["start","end","center","between","around","evenly","stretch"],U=()=>["","0",A],je=()=>["auto","avoid","all","avoid-page","page","left","right","column"],z=()=>[qe,A];return{cacheSize:500,separator:":",theme:{colors:[dt],spacing:[ye,Ne],blur:["none","",Se,A],brightness:z(),borderColor:[e],borderRadius:["none","","full",Se,A],borderSpacing:k(),borderWidth:Z(),contrast:z(),grayscale:U(),hueRotate:z(),invert:U(),gap:k(),gradientColorStops:[e],gradientColorStopPositions:[ii,Ne],inset:O(),margin:O(),opacity:z(),padding:k(),saturate:z(),scale:z(),sepia:U(),skew:z(),space:k(),translate:k()},classGroups:{aspect:[{aspect:["auto","square","video",A]}],container:["container"],columns:[{columns:[Se]}],"break-after":[{"break-after":je()}],"break-before":[{"break-before":je()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...G(),A]}],overflow:[{overflow:Q()}],"overflow-x":[{"overflow-x":Q()}],"overflow-y":[{"overflow-y":Q()}],overscroll:[{overscroll:W()}],"overscroll-x":[{"overscroll-x":W()}],"overscroll-y":[{"overscroll-y":W()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",ct,A]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",A]}],grow:[{grow:U()}],shrink:[{shrink:U()}],order:[{order:["first","last","none",ct,A]}],"grid-cols":[{"grid-cols":[dt]}],"col-start-end":[{col:["auto",{span:["full",ct,A]},A]}],"col-start":[{"col-start":T()}],"col-end":[{"col-end":T()}],"grid-rows":[{"grid-rows":[dt]}],"row-start-end":[{row:["auto",{span:[ct,A]},A]}],"row-start":[{"row-start":T()}],"row-end":[{"row-end":T()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",A]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",A]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",...E()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...E(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...E(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[N]}],px:[{px:[N]}],py:[{py:[N]}],ps:[{ps:[N]}],pe:[{pe:[N]}],pt:[{pt:[N]}],pr:[{pr:[N]}],pb:[{pb:[N]}],pl:[{pl:[N]}],m:[{m:[f]}],mx:[{mx:[f]}],my:[{my:[f]}],ms:[{ms:[f]}],me:[{me:[f]}],mt:[{mt:[f]}],mr:[{mr:[f]}],mb:[{mb:[f]}],ml:[{ml:[f]}],"space-x":[{"space-x":[L]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[L]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",A,t]}],"min-w":[{"min-w":[A,t,"min","max","fit"]}],"max-w":[{"max-w":[A,t,"none","full","min","max","fit","prose",{screen:[Se]},Se]}],h:[{h:[A,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[A,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[A,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[A,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Se,Ne]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",ar]}],"font-family":[{font:[dt]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",A]}],"line-clamp":[{"line-clamp":["none",qe,ar]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",ye,A]}],"list-image":[{"list-image":["none",A]}],"list-style-type":[{list:["none","disc","decimal",A]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",ye,Ne]}],"underline-offset":[{"underline-offset":["auto",ye,A]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",A]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",A]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...G(),di]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",ci]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},hi]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[b]}],"gradient-via":[{via:[b]}],"gradient-to":[{to:[b]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...B(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:B()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...B()]}],"outline-offset":[{"outline-offset":[ye,A]}],"outline-w":[{outline:[ye,Ne]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:Z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[ye,Ne]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Se,mi]}],"shadow-color":[{shadow:[dt]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...J(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":J()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[d]}],"drop-shadow":[{"drop-shadow":["","none",Se,A]}],grayscale:[{grayscale:[h]}],"hue-rotate":[{"hue-rotate":[g]}],invert:[{invert:[p]}],saturate:[{saturate:[w]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[d]}],"backdrop-grayscale":[{"backdrop-grayscale":[h]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[g]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",A]}],duration:[{duration:z()}],ease:[{ease:["linear","in","out","in-out",A]}],delay:[{delay:z()}],animate:[{animate:["none","spin","ping","pulse","bounce",A]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[j]}],"scale-x":[{"scale-x":[j]}],"scale-y":[{"scale-y":[j]}],rotate:[{rotate:[ct,A]}],"translate-x":[{"translate-x":[I]}],"translate-y":[{"translate-y":[I]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",A]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",A]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",A]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[ye,Ne,ar]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},bi=Za(xi);function fe(...e){return bi(Js(e))}const vi=ma,co=u.forwardRef(({className:e,...t},r)=>s.jsx(Gs,{ref:r,className:fe("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));co.displayName=Gs.displayName;const yi=Zs("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),uo=u.forwardRef(({className:e,variant:t,...r},o)=>s.jsx(qs,{ref:o,className:fe(yi({variant:t}),e),...r}));uo.displayName=qs.displayName;const wi=u.forwardRef(({className:e,...t},r)=>s.jsx(Ks,{ref:r,className:fe("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));wi.displayName=Ks.displayName;const ho=u.forwardRef(({className:e,...t},r)=>s.jsx(Qs,{ref:r,className:fe("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:s.jsx(yt,{className:"h-4 w-4"})}));ho.displayName=Qs.displayName;const mo=u.forwardRef(({className:e,...t},r)=>s.jsx(Us,{ref:r,className:fe("text-sm font-semibold",e),...t}));mo.displayName=Us.displayName;const fo=u.forwardRef(({className:e,...t},r)=>s.jsx(Ys,{ref:r,className:fe("text-sm opacity-90",e),...t}));fo.displayName=Ys.displayName;function ji(){const{toasts:e}=Un();return s.jsxs(vi,{children:[e.map(function({id:t,title:r,description:o,action:n,...a}){return s.jsxs(uo,{...a,children:[s.jsxs("div",{className:"grid gap-1",children:[r&&s.jsx(mo,{children:r}),o&&s.jsx(fo,{children:o})]}),n,s.jsx(ho,{})]},t)}),s.jsx(co,{})]})}var ts=["light","dark"],Ci="(prefers-color-scheme: dark)",Ni=u.createContext(void 0),Si={setTheme:e=>{},themes:[]},Ti=()=>{var e;return(e=u.useContext(Ni))!=null?e:Si};u.memo(({forcedTheme:e,storageKey:t,attribute:r,enableSystem:o,enableColorScheme:n,defaultTheme:a,value:i,attrs:l,nonce:d})=>{let h=a==="system",g=r==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${l.map(x=>`'${x}'`).join(",")})`};`:`var d=document.documentElement,n='${r}',s='setAttribute';`,p=n?ts.includes(a)&&a?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${a}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",c=(x,m=!1,f=!0)=>{let y=i?i[x]:x,N=m?x+"|| ''":`'${y}'`,w="";return n&&f&&!m&&ts.includes(x)&&(w+=`d.style.colorScheme = '${x}';`),r==="class"?m||y?w+=`c.add(${N})`:w+="null":y&&(w+=`d[s](n,${N})`),w},b=e?`!function(){${g}${c(e)}}()`:o?`!function(){try{${g}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${h})){var t='${Ci}',m=window.matchMedia(t);if(m.media!==t||m.matches){${c("dark")}}else{${c("light")}}}else if(e){${i?`var x=${JSON.stringify(i)};`:""}${c(i?"x[e]":"e",!0)}}${h?"":"else{"+c(a,!1,!1)+"}"}${p}}catch(e){}}()`:`!function(){try{${g}var e=localStorage.getItem('${t}');if(e){${i?`var x=${JSON.stringify(i)};`:""}${c(i?"x[e]":"e",!0)}}else{${c(a,!1,!1)};}${p}}catch(t){}}();`;return u.createElement("script",{nonce:d,dangerouslySetInnerHTML:{__html:b}})});var Ei=e=>{switch(e){case"success":return Mi;case"info":return Ri;case"warning":return Ai;case"error":return zi;default:return null}},ki=Array(12).fill(0),Pi=({visible:e})=>C.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},C.createElement("div",{className:"sonner-spinner"},ki.map((t,r)=>C.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${r}`})))),Mi=C.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},C.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Ai=C.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},C.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),Ri=C.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},C.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),zi=C.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},C.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),Ii=()=>{let[e,t]=C.useState(document.hidden);return C.useEffect(()=>{let r=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",r),()=>window.removeEventListener("visibilitychange",r)},[]),e},gr=1,Di=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...o}=e,n=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:gr++,a=this.toasts.find(l=>l.id===n),i=e.dismissible===void 0?!0:e.dismissible;return a?this.toasts=this.toasts.map(l=>l.id===n?(this.publish({...l,...e,id:n,title:r}),{...l,...e,id:n,dismissible:i,title:r}):l):this.addToast({title:r,...o,dismissible:i,id:n}),n},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(r=>r({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let r;t.loading!==void 0&&(r=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let o=e instanceof Promise?e:e(),n=r!==void 0;return o.then(async a=>{if(Fi(a)&&!a.ok){n=!1;let i=typeof t.error=="function"?await t.error(`HTTP error! status: ${a.status}`):t.error,l=typeof t.description=="function"?await t.description(`HTTP error! status: ${a.status}`):t.description;this.create({id:r,type:"error",message:i,description:l})}else if(t.success!==void 0){n=!1;let i=typeof t.success=="function"?await t.success(a):t.success,l=typeof t.description=="function"?await t.description(a):t.description;this.create({id:r,type:"success",message:i,description:l})}}).catch(async a=>{if(t.error!==void 0){n=!1;let i=typeof t.error=="function"?await t.error(a):t.error,l=typeof t.description=="function"?await t.description(a):t.description;this.create({id:r,type:"error",message:i,description:l})}}).finally(()=>{var a;n&&(this.dismiss(r),r=void 0),(a=t.finally)==null||a.call(t)}),r},this.custom=(e,t)=>{let r=(t==null?void 0:t.id)||gr++;return this.create({jsx:e(r),id:r,...t}),r},this.subscribers=[],this.toasts=[]}},le=new Di,Oi=(e,t)=>{let r=(t==null?void 0:t.id)||gr++;return le.addToast({title:e,...t,id:r}),r},Fi=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Li=Oi,_i=()=>le.toasts,Bi=Object.assign(Li,{success:le.success,info:le.info,warning:le.warning,error:le.error,custom:le.custom,message:le.message,promise:le.promise,dismiss:le.dismiss,loading:le.loading},{getHistory:_i});function Vi(e,{insertAt:t}={}){if(typeof document>"u")return;let r=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css",t==="top"&&r.firstChild?r.insertBefore(o,r.firstChild):r.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}Vi(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Tt(e){return e.label!==void 0}var Hi=3,Wi="32px",$i=4e3,Gi=356,qi=14,Ui=20,Yi=200;function Ki(...e){return e.filter(Boolean).join(" ")}var Qi=e=>{var t,r,o,n,a,i,l,d,h,g;let{invert:p,toast:c,unstyled:b,interacting:x,setHeights:m,visibleToasts:f,heights:y,index:N,toasts:w,expanded:j,removeToast:S,defaultRichColors:P,closeButton:L,style:I,cancelButtonStyle:W,actionButtonStyle:Q,className:O="",descriptionClassName:k="",duration:Z,position:T,gap:G,loadingIcon:B,expandByDefault:J,classNames:E,icons:U,closeButtonAriaLabel:je="Close toast",pauseWhenPageIsHidden:z,cn:V}=e,[H,ae]=C.useState(!1),[ze,ee]=C.useState(!1),[Xt,at]=C.useState(!1),[it,lt]=C.useState(!1),[fn,Jt]=C.useState(0),[pn,_r]=C.useState(0),Br=C.useRef(null),Be=C.useRef(null),gn=N===0,xn=N+1<=f,ce=c.type,Ve=c.dismissible!==!1,bn=c.className||"",vn=c.descriptionClassName||"",jt=C.useMemo(()=>y.findIndex(M=>M.toastId===c.id)||0,[y,c.id]),yn=C.useMemo(()=>{var M;return(M=c.closeButton)!=null?M:L},[c.closeButton,L]),Vr=C.useMemo(()=>c.duration||Z||$i,[c.duration,Z]),Zt=C.useRef(0),He=C.useRef(0),Hr=C.useRef(0),We=C.useRef(null),[Wr,wn]=T.split("-"),$r=C.useMemo(()=>y.reduce((M,Y,q)=>q>=jt?M:M+Y.height,0),[y,jt]),Gr=Ii(),jn=c.invert||p,er=ce==="loading";He.current=C.useMemo(()=>jt*G+$r,[jt,$r]),C.useEffect(()=>{ae(!0)},[]),C.useLayoutEffect(()=>{if(!H)return;let M=Be.current,Y=M.style.height;M.style.height="auto";let q=M.getBoundingClientRect().height;M.style.height=Y,_r(q),m(pe=>pe.find(ge=>ge.toastId===c.id)?pe.map(ge=>ge.toastId===c.id?{...ge,height:q}:ge):[{toastId:c.id,height:q,position:c.position},...pe])},[H,c.title,c.description,m,c.id]);let Ce=C.useCallback(()=>{ee(!0),Jt(He.current),m(M=>M.filter(Y=>Y.toastId!==c.id)),setTimeout(()=>{S(c)},Yi)},[c,S,m,He]);C.useEffect(()=>{if(c.promise&&ce==="loading"||c.duration===1/0||c.type==="loading")return;let M,Y=Vr;return j||x||z&&Gr?(()=>{if(Hr.current<Zt.current){let q=new Date().getTime()-Zt.current;Y=Y-q}Hr.current=new Date().getTime()})():Y!==1/0&&(Zt.current=new Date().getTime(),M=setTimeout(()=>{var q;(q=c.onAutoClose)==null||q.call(c,c),Ce()},Y)),()=>clearTimeout(M)},[j,x,J,c,Vr,Ce,c.promise,ce,z,Gr]),C.useEffect(()=>{let M=Be.current;if(M){let Y=M.getBoundingClientRect().height;return _r(Y),m(q=>[{toastId:c.id,height:Y,position:c.position},...q]),()=>m(q=>q.filter(pe=>pe.toastId!==c.id))}},[m,c.id]),C.useEffect(()=>{c.delete&&Ce()},[Ce,c.delete]);function Cn(){return U!=null&&U.loading?C.createElement("div",{className:"sonner-loader","data-visible":ce==="loading"},U.loading):B?C.createElement("div",{className:"sonner-loader","data-visible":ce==="loading"},B):C.createElement(Pi,{visible:ce==="loading"})}return C.createElement("li",{"aria-live":c.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:Be,className:V(O,bn,E==null?void 0:E.toast,(t=c==null?void 0:c.classNames)==null?void 0:t.toast,E==null?void 0:E.default,E==null?void 0:E[ce],(r=c==null?void 0:c.classNames)==null?void 0:r[ce]),"data-sonner-toast":"","data-rich-colors":(o=c.richColors)!=null?o:P,"data-styled":!(c.jsx||c.unstyled||b),"data-mounted":H,"data-promise":!!c.promise,"data-removed":ze,"data-visible":xn,"data-y-position":Wr,"data-x-position":wn,"data-index":N,"data-front":gn,"data-swiping":Xt,"data-dismissible":Ve,"data-type":ce,"data-invert":jn,"data-swipe-out":it,"data-expanded":!!(j||J&&H),style:{"--index":N,"--toasts-before":N,"--z-index":w.length-N,"--offset":`${ze?fn:He.current}px`,"--initial-height":J?"auto":`${pn}px`,...I,...c.style},onPointerDown:M=>{er||!Ve||(Br.current=new Date,Jt(He.current),M.target.setPointerCapture(M.pointerId),M.target.tagName!=="BUTTON"&&(at(!0),We.current={x:M.clientX,y:M.clientY}))},onPointerUp:()=>{var M,Y,q,pe;if(it||!Ve)return;We.current=null;let ge=Number(((M=Be.current)==null?void 0:M.style.getPropertyValue("--swipe-amount").replace("px",""))||0),Ct=new Date().getTime()-((Y=Br.current)==null?void 0:Y.getTime()),Nn=Math.abs(ge)/Ct;if(Math.abs(ge)>=Ui||Nn>.11){Jt(He.current),(q=c.onDismiss)==null||q.call(c,c),Ce(),lt(!0);return}(pe=Be.current)==null||pe.style.setProperty("--swipe-amount","0px"),at(!1)},onPointerMove:M=>{var Y;if(!We.current||!Ve)return;let q=M.clientY-We.current.y,pe=M.clientX-We.current.x,ge=(Wr==="top"?Math.min:Math.max)(0,q),Ct=M.pointerType==="touch"?10:2;Math.abs(ge)>Ct?(Y=Be.current)==null||Y.style.setProperty("--swipe-amount",`${q}px`):Math.abs(pe)>Ct&&(We.current=null)}},yn&&!c.jsx?C.createElement("button",{"aria-label":je,"data-disabled":er,"data-close-button":!0,onClick:er||!Ve?()=>{}:()=>{var M;Ce(),(M=c.onDismiss)==null||M.call(c,c)},className:V(E==null?void 0:E.closeButton,(n=c==null?void 0:c.classNames)==null?void 0:n.closeButton)},C.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},C.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),C.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,c.jsx||C.isValidElement(c.title)?c.jsx||c.title:C.createElement(C.Fragment,null,ce||c.icon||c.promise?C.createElement("div",{"data-icon":"",className:V(E==null?void 0:E.icon,(a=c==null?void 0:c.classNames)==null?void 0:a.icon)},c.promise||c.type==="loading"&&!c.icon?c.icon||Cn():null,c.type!=="loading"?c.icon||(U==null?void 0:U[ce])||Ei(ce):null):null,C.createElement("div",{"data-content":"",className:V(E==null?void 0:E.content,(i=c==null?void 0:c.classNames)==null?void 0:i.content)},C.createElement("div",{"data-title":"",className:V(E==null?void 0:E.title,(l=c==null?void 0:c.classNames)==null?void 0:l.title)},c.title),c.description?C.createElement("div",{"data-description":"",className:V(k,vn,E==null?void 0:E.description,(d=c==null?void 0:c.classNames)==null?void 0:d.description)},c.description):null),C.isValidElement(c.cancel)?c.cancel:c.cancel&&Tt(c.cancel)?C.createElement("button",{"data-button":!0,"data-cancel":!0,style:c.cancelButtonStyle||W,onClick:M=>{var Y,q;Tt(c.cancel)&&Ve&&((q=(Y=c.cancel).onClick)==null||q.call(Y,M),Ce())},className:V(E==null?void 0:E.cancelButton,(h=c==null?void 0:c.classNames)==null?void 0:h.cancelButton)},c.cancel.label):null,C.isValidElement(c.action)?c.action:c.action&&Tt(c.action)?C.createElement("button",{"data-button":!0,"data-action":!0,style:c.actionButtonStyle||Q,onClick:M=>{var Y,q;Tt(c.action)&&(M.defaultPrevented||((q=(Y=c.action).onClick)==null||q.call(Y,M),Ce()))},className:V(E==null?void 0:E.actionButton,(g=c==null?void 0:c.classNames)==null?void 0:g.actionButton)},c.action.label):null))};function rs(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var Xi=e=>{let{invert:t,position:r="bottom-right",hotkey:o=["altKey","KeyT"],expand:n,closeButton:a,className:i,offset:l,theme:d="light",richColors:h,duration:g,style:p,visibleToasts:c=Hi,toastOptions:b,dir:x=rs(),gap:m=qi,loadingIcon:f,icons:y,containerAriaLabel:N="Notifications",pauseWhenPageIsHidden:w,cn:j=Ki}=e,[S,P]=C.useState([]),L=C.useMemo(()=>Array.from(new Set([r].concat(S.filter(z=>z.position).map(z=>z.position)))),[S,r]),[I,W]=C.useState([]),[Q,O]=C.useState(!1),[k,Z]=C.useState(!1),[T,G]=C.useState(d!=="system"?d:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),B=C.useRef(null),J=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=C.useRef(null),U=C.useRef(!1),je=C.useCallback(z=>{var V;(V=S.find(H=>H.id===z.id))!=null&&V.delete||le.dismiss(z.id),P(H=>H.filter(({id:ae})=>ae!==z.id))},[S]);return C.useEffect(()=>le.subscribe(z=>{if(z.dismiss){P(V=>V.map(H=>H.id===z.id?{...H,delete:!0}:H));return}setTimeout(()=>{Ln.flushSync(()=>{P(V=>{let H=V.findIndex(ae=>ae.id===z.id);return H!==-1?[...V.slice(0,H),{...V[H],...z},...V.slice(H+1)]:[z,...V]})})})}),[]),C.useEffect(()=>{if(d!=="system"){G(d);return}d==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?G("dark"):G("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:z})=>{G(z?"dark":"light")})},[d]),C.useEffect(()=>{S.length<=1&&O(!1)},[S]),C.useEffect(()=>{let z=V=>{var H,ae;o.every(ze=>V[ze]||V.code===ze)&&(O(!0),(H=B.current)==null||H.focus()),V.code==="Escape"&&(document.activeElement===B.current||(ae=B.current)!=null&&ae.contains(document.activeElement))&&O(!1)};return document.addEventListener("keydown",z),()=>document.removeEventListener("keydown",z)},[o]),C.useEffect(()=>{if(B.current)return()=>{E.current&&(E.current.focus({preventScroll:!0}),E.current=null,U.current=!1)}},[B.current]),S.length?C.createElement("section",{"aria-label":`${N} ${J}`,tabIndex:-1},L.map((z,V)=>{var H;let[ae,ze]=z.split("-");return C.createElement("ol",{key:z,dir:x==="auto"?rs():x,tabIndex:-1,ref:B,className:i,"data-sonner-toaster":!0,"data-theme":T,"data-y-position":ae,"data-x-position":ze,style:{"--front-toast-height":`${((H=I[0])==null?void 0:H.height)||0}px`,"--offset":typeof l=="number"?`${l}px`:l||Wi,"--width":`${Gi}px`,"--gap":`${m}px`,...p},onBlur:ee=>{U.current&&!ee.currentTarget.contains(ee.relatedTarget)&&(U.current=!1,E.current&&(E.current.focus({preventScroll:!0}),E.current=null))},onFocus:ee=>{ee.target instanceof HTMLElement&&ee.target.dataset.dismissible==="false"||U.current||(U.current=!0,E.current=ee.relatedTarget)},onMouseEnter:()=>O(!0),onMouseMove:()=>O(!0),onMouseLeave:()=>{k||O(!1)},onPointerDown:ee=>{ee.target instanceof HTMLElement&&ee.target.dataset.dismissible==="false"||Z(!0)},onPointerUp:()=>Z(!1)},S.filter(ee=>!ee.position&&V===0||ee.position===z).map((ee,Xt)=>{var at,it;return C.createElement(Qi,{key:ee.id,icons:y,index:Xt,toast:ee,defaultRichColors:h,duration:(at=b==null?void 0:b.duration)!=null?at:g,className:b==null?void 0:b.className,descriptionClassName:b==null?void 0:b.descriptionClassName,invert:t,visibleToasts:c,closeButton:(it=b==null?void 0:b.closeButton)!=null?it:a,interacting:k,position:z,style:b==null?void 0:b.style,unstyled:b==null?void 0:b.unstyled,classNames:b==null?void 0:b.classNames,cancelButtonStyle:b==null?void 0:b.cancelButtonStyle,actionButtonStyle:b==null?void 0:b.actionButtonStyle,removeToast:je,toasts:S.filter(lt=>lt.position==ee.position),heights:I.filter(lt=>lt.position==ee.position),setHeights:W,expandByDefault:n,gap:m,loadingIcon:f,expanded:Q,pauseWhenPageIsHidden:w,cn:j})}))})):null};const Ji=({...e})=>{const{theme:t="system"}=Ti();return s.jsx(Xi,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var[qt,l1]=_t("Tooltip",[Vt]),Pr=Vt(),po="TooltipProvider",Zi=700,ss="tooltip.open",[e0,go]=qt(po),xo=e=>{const{__scopeTooltip:t,delayDuration:r=Zi,skipDelayDuration:o=300,disableHoverableContent:n=!1,children:a}=e,[i,l]=u.useState(!0),d=u.useRef(!1),h=u.useRef(0);return u.useEffect(()=>{const g=h.current;return()=>window.clearTimeout(g)},[]),s.jsx(e0,{scope:t,isOpenDelayed:i,delayDuration:r,onOpen:u.useCallback(()=>{window.clearTimeout(h.current),l(!1)},[]),onClose:u.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>l(!0),o)},[o]),isPointerInTransitRef:d,onPointerInTransitChange:u.useCallback(g=>{d.current=g},[]),disableHoverableContent:n,children:a})};xo.displayName=po;var bo="Tooltip",[c1,Ut]=qt(bo),xr="TooltipTrigger",t0=u.forwardRef((e,t)=>{const{__scopeTooltip:r,...o}=e,n=Ut(xr,r),a=go(xr,r),i=Pr(r),l=u.useRef(null),d=me(t,l,n.onTriggerChange),h=u.useRef(!1),g=u.useRef(!1),p=u.useCallback(()=>h.current=!1,[]);return u.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),s.jsx(Cr,{asChild:!0,...i,children:s.jsx(se.button,{"aria-describedby":n.open?n.contentId:void 0,"data-state":n.stateAttribute,...o,ref:d,onPointerMove:$(e.onPointerMove,c=>{c.pointerType!=="touch"&&!g.current&&!a.isPointerInTransitRef.current&&(n.onTriggerEnter(),g.current=!0)}),onPointerLeave:$(e.onPointerLeave,()=>{n.onTriggerLeave(),g.current=!1}),onPointerDown:$(e.onPointerDown,()=>{h.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:$(e.onFocus,()=>{h.current||n.onOpen()}),onBlur:$(e.onBlur,n.onClose),onClick:$(e.onClick,n.onClose)})})});t0.displayName=xr;var r0="TooltipPortal",[d1,s0]=qt(r0,{forceMount:void 0}),rt="TooltipContent",vo=u.forwardRef((e,t)=>{const r=s0(rt,e.__scopeTooltip),{forceMount:o=r.forceMount,side:n="top",...a}=e,i=Ut(rt,e.__scopeTooltip);return s.jsx(Bt,{present:o||i.open,children:i.disableHoverableContent?s.jsx(yo,{side:n,...a,ref:t}):s.jsx(o0,{side:n,...a,ref:t})})}),o0=u.forwardRef((e,t)=>{const r=Ut(rt,e.__scopeTooltip),o=go(rt,e.__scopeTooltip),n=u.useRef(null),a=me(t,n),[i,l]=u.useState(null),{trigger:d,onClose:h}=r,g=n.current,{onPointerInTransitChange:p}=o,c=u.useCallback(()=>{l(null),p(!1)},[p]),b=u.useCallback((x,m)=>{const f=x.currentTarget,y={x:x.clientX,y:x.clientY},N=l0(y,f.getBoundingClientRect()),w=c0(y,N),j=d0(m.getBoundingClientRect()),S=h0([...w,...j]);l(S),p(!0)},[p]);return u.useEffect(()=>()=>c(),[c]),u.useEffect(()=>{if(d&&g){const x=f=>b(f,g),m=f=>b(f,d);return d.addEventListener("pointerleave",x),g.addEventListener("pointerleave",m),()=>{d.removeEventListener("pointerleave",x),g.removeEventListener("pointerleave",m)}}},[d,g,b,c]),u.useEffect(()=>{if(i){const x=m=>{const f=m.target,y={x:m.clientX,y:m.clientY},N=(d==null?void 0:d.contains(f))||(g==null?void 0:g.contains(f)),w=!u0(y,i);N?c():w&&(c(),h())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[d,g,i,h,c]),s.jsx(yo,{...e,ref:a})}),[n0,a0]=qt(bo,{isInside:!1}),yo=u.forwardRef((e,t)=>{const{__scopeTooltip:r,children:o,"aria-label":n,onEscapeKeyDown:a,onPointerDownOutside:i,...l}=e,d=Ut(rt,r),h=Pr(r),{onClose:g}=d;return u.useEffect(()=>(document.addEventListener(ss,g),()=>document.removeEventListener(ss,g)),[g]),u.useEffect(()=>{if(d.trigger){const p=c=>{const b=c.target;b!=null&&b.contains(d.trigger)&&g()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[d.trigger,g]),s.jsx(Ts,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:p=>p.preventDefault(),onDismiss:g,children:s.jsxs(Es,{"data-state":d.stateAttribute,...h,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[s.jsx(Pn,{children:o}),s.jsx(n0,{scope:r,isInside:!0,children:s.jsx(Kn,{id:d.contentId,role:"tooltip",children:n||o})})]})})});vo.displayName=rt;var wo="TooltipArrow",i0=u.forwardRef((e,t)=>{const{__scopeTooltip:r,...o}=e,n=Pr(r);return a0(wo,r).isInside?null:s.jsx(ks,{...n,...o,ref:t})});i0.displayName=wo;function l0(e,t){const r=Math.abs(t.top-e.y),o=Math.abs(t.bottom-e.y),n=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,o,n,a)){case a:return"left";case n:return"right";case r:return"top";case o:return"bottom";default:throw new Error("unreachable")}}function c0(e,t,r=5){const o=[];switch(t){case"top":o.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":o.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":o.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":o.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return o}function d0(e){const{top:t,right:r,bottom:o,left:n}=e;return[{x:n,y:t},{x:r,y:t},{x:r,y:o},{x:n,y:o}]}function u0(e,t){const{x:r,y:o}=e;let n=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const l=t[a].x,d=t[a].y,h=t[i].x,g=t[i].y;d>o!=g>o&&r<(h-l)*(o-d)/(g-d)+l&&(n=!n)}return n}function h0(e){const t=e.slice();return t.sort((r,o)=>r.x<o.x?-1:r.x>o.x?1:r.y<o.y?-1:r.y>o.y?1:0),m0(t)}function m0(e){if(e.length<=1)return e.slice();const t=[];for(let o=0;o<e.length;o++){const n=e[o];for(;t.length>=2;){const a=t[t.length-1],i=t[t.length-2];if((a.x-i.x)*(n.y-i.y)>=(a.y-i.y)*(n.x-i.x))t.pop();else break}t.push(n)}t.pop();const r=[];for(let o=e.length-1;o>=0;o--){const n=e[o];for(;r.length>=2;){const a=r[r.length-1],i=r[r.length-2];if((a.x-i.x)*(n.y-i.y)>=(a.y-i.y)*(n.x-i.x))r.pop();else break}r.push(n)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var f0=xo,jo=vo;const p0=f0,g0=u.forwardRef(({className:e,sideOffset:t=4,...r},o)=>s.jsx(jo,{ref:o,sideOffset:t,className:fe("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));g0.displayName=jo.displayName;var Yt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Kt=typeof window>"u"||"Deno"in globalThis;function ue(){}function x0(e,t){return typeof e=="function"?e(t):e}function b0(e){return typeof e=="number"&&e>=0&&e!==1/0}function v0(e,t){return Math.max(e+(t||0)-Date.now(),0)}function os(e,t){return typeof e=="function"?e(t):e}function y0(e,t){return typeof e=="function"?e(t):e}function ns(e,t){const{type:r="all",exact:o,fetchStatus:n,predicate:a,queryKey:i,stale:l}=e;if(i){if(o){if(t.queryHash!==Mr(i,t.options))return!1}else if(!xt(t.queryKey,i))return!1}if(r!=="all"){const d=t.isActive();if(r==="active"&&!d||r==="inactive"&&d)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||n&&n!==t.state.fetchStatus||a&&!a(t))}function as(e,t){const{exact:r,status:o,predicate:n,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(r){if(gt(t.options.mutationKey)!==gt(a))return!1}else if(!xt(t.options.mutationKey,a))return!1}return!(o&&t.state.status!==o||n&&!n(t))}function Mr(e,t){return((t==null?void 0:t.queryKeyHashFn)||gt)(e)}function gt(e){return JSON.stringify(e,(t,r)=>br(r)?Object.keys(r).sort().reduce((o,n)=>(o[n]=r[n],o),{}):r)}function xt(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(r=>!xt(e[r],t[r])):!1}function Co(e,t){if(e===t)return e;const r=is(e)&&is(t);if(r||br(e)&&br(t)){const o=r?e:Object.keys(e),n=o.length,a=r?t:Object.keys(t),i=a.length,l=r?[]:{};let d=0;for(let h=0;h<i;h++){const g=r?h:a[h];(!r&&o.includes(g)||r)&&e[g]===void 0&&t[g]===void 0?(l[g]=void 0,d++):(l[g]=Co(e[g],t[g]),l[g]===e[g]&&e[g]!==void 0&&d++)}return n===i&&d===n?e:l}return t}function is(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function br(e){if(!ls(e))return!1;const t=e.constructor;if(t===void 0)return!0;const r=t.prototype;return!(!ls(r)||!r.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function ls(e){return Object.prototype.toString.call(e)==="[object Object]"}function w0(e){return new Promise(t=>{setTimeout(t,e)})}function j0(e,t,r){return typeof r.structuralSharing=="function"?r.structuralSharing(e,t):r.structuralSharing!==!1?Co(e,t):t}function C0(e,t,r=0){const o=[...e,t];return r&&o.length>r?o.slice(1):o}function N0(e,t,r=0){const o=[t,...e];return r&&o.length>r?o.slice(0,-1):o}var Ar=Symbol();function No(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===Ar?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var Oe,Ee,Ue,gs,S0=(gs=class extends Yt{constructor(){super();_(this,Oe);_(this,Ee);_(this,Ue);R(this,Ue,t=>{if(!Kt&&window.addEventListener){const r=()=>t();return window.addEventListener("visibilitychange",r,!1),()=>{window.removeEventListener("visibilitychange",r)}}})}onSubscribe(){v(this,Ee)||this.setEventListener(v(this,Ue))}onUnsubscribe(){var t;this.hasListeners()||((t=v(this,Ee))==null||t.call(this),R(this,Ee,void 0))}setEventListener(t){var r;R(this,Ue,t),(r=v(this,Ee))==null||r.call(this),R(this,Ee,t(o=>{typeof o=="boolean"?this.setFocused(o):this.onFocus()}))}setFocused(t){v(this,Oe)!==t&&(R(this,Oe,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(r=>{r(t)})}isFocused(){var t;return typeof v(this,Oe)=="boolean"?v(this,Oe):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Oe=new WeakMap,Ee=new WeakMap,Ue=new WeakMap,gs),So=new S0,Ye,ke,Ke,xs,T0=(xs=class extends Yt{constructor(){super();_(this,Ye,!0);_(this,ke);_(this,Ke);R(this,Ke,t=>{if(!Kt&&window.addEventListener){const r=()=>t(!0),o=()=>t(!1);return window.addEventListener("online",r,!1),window.addEventListener("offline",o,!1),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",o)}}})}onSubscribe(){v(this,ke)||this.setEventListener(v(this,Ke))}onUnsubscribe(){var t;this.hasListeners()||((t=v(this,ke))==null||t.call(this),R(this,ke,void 0))}setEventListener(t){var r;R(this,Ke,t),(r=v(this,ke))==null||r.call(this),R(this,ke,t(this.setOnline.bind(this)))}setOnline(t){v(this,Ye)!==t&&(R(this,Ye,t),this.listeners.forEach(o=>{o(t)}))}isOnline(){return v(this,Ye)}},Ye=new WeakMap,ke=new WeakMap,Ke=new WeakMap,xs),Dt=new T0;function E0(){let e,t;const r=new Promise((n,a)=>{e=n,t=a});r.status="pending",r.catch(()=>{});function o(n){Object.assign(r,n),delete r.resolve,delete r.reject}return r.resolve=n=>{o({status:"fulfilled",value:n}),e(n)},r.reject=n=>{o({status:"rejected",reason:n}),t(n)},r}function k0(e){return Math.min(1e3*2**e,3e4)}function To(e){return(e??"online")==="online"?Dt.isOnline():!0}var Eo=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function ir(e){return e instanceof Eo}function ko(e){let t=!1,r=0,o=!1,n;const a=E0(),i=m=>{var f;o||(c(new Eo(m)),(f=e.abort)==null||f.call(e))},l=()=>{t=!0},d=()=>{t=!1},h=()=>So.isFocused()&&(e.networkMode==="always"||Dt.isOnline())&&e.canRun(),g=()=>To(e.networkMode)&&e.canRun(),p=m=>{var f;o||(o=!0,(f=e.onSuccess)==null||f.call(e,m),n==null||n(),a.resolve(m))},c=m=>{var f;o||(o=!0,(f=e.onError)==null||f.call(e,m),n==null||n(),a.reject(m))},b=()=>new Promise(m=>{var f;n=y=>{(o||h())&&m(y)},(f=e.onPause)==null||f.call(e)}).then(()=>{var m;n=void 0,o||(m=e.onContinue)==null||m.call(e)}),x=()=>{if(o)return;let m;const f=r===0?e.initialPromise:void 0;try{m=f??e.fn()}catch(y){m=Promise.reject(y)}Promise.resolve(m).then(p).catch(y=>{var P;if(o)return;const N=e.retry??(Kt?0:3),w=e.retryDelay??k0,j=typeof w=="function"?w(r,y):w,S=N===!0||typeof N=="number"&&r<N||typeof N=="function"&&N(r,y);if(t||!S){c(y);return}r++,(P=e.onFail)==null||P.call(e,r,y),w0(j).then(()=>h()?void 0:b()).then(()=>{t?c(y):x()})})};return{promise:a,cancel:i,continue:()=>(n==null||n(),a),cancelRetry:l,continueRetry:d,canStart:g,start:()=>(g()?x():b().then(x),a)}}function P0(){let e=[],t=0,r=l=>{l()},o=l=>{l()},n=l=>setTimeout(l,0);const a=l=>{t?e.push(l):n(()=>{r(l)})},i=()=>{const l=e;e=[],l.length&&n(()=>{o(()=>{l.forEach(d=>{r(d)})})})};return{batch:l=>{let d;t++;try{d=l()}finally{t--,t||i()}return d},batchCalls:l=>(...d)=>{a(()=>{l(...d)})},schedule:a,setNotifyFunction:l=>{r=l},setBatchNotifyFunction:l=>{o=l},setScheduler:l=>{n=l}}}var ne=P0(),Fe,bs,Po=(bs=class{constructor(){_(this,Fe)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),b0(this.gcTime)&&R(this,Fe,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Kt?1/0:5*60*1e3))}clearGcTimeout(){v(this,Fe)&&(clearTimeout(v(this,Fe)),R(this,Fe,void 0))}},Fe=new WeakMap,bs),Qe,Xe,de,re,bt,Le,he,we,vs,M0=(vs=class extends Po{constructor(t){super();_(this,he);_(this,Qe);_(this,Xe);_(this,de);_(this,re);_(this,bt);_(this,Le);R(this,Le,!1),R(this,bt,t.defaultOptions),this.setOptions(t.options),this.observers=[],R(this,de,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,R(this,Qe,R0(this.options)),this.state=t.state??v(this,Qe),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=v(this,re))==null?void 0:t.promise}setOptions(t){this.options={...v(this,bt),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&v(this,de).remove(this)}setData(t,r){const o=j0(this.state.data,t,this.options);return te(this,he,we).call(this,{data:o,type:"success",dataUpdatedAt:r==null?void 0:r.updatedAt,manual:r==null?void 0:r.manual}),o}setState(t,r){te(this,he,we).call(this,{type:"setState",state:t,setStateOptions:r})}cancel(t){var o,n;const r=(o=v(this,re))==null?void 0:o.promise;return(n=v(this,re))==null||n.cancel(t),r?r.then(ue).catch(ue):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(v(this,Qe))}isActive(){return this.observers.some(t=>y0(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Ar||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!v0(this.state.dataUpdatedAt,t)}onFocus(){var r;const t=this.observers.find(o=>o.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(r=v(this,re))==null||r.continue()}onOnline(){var r;const t=this.observers.find(o=>o.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(r=v(this,re))==null||r.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),v(this,de).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(r=>r!==t),this.observers.length||(v(this,re)&&(v(this,Le)?v(this,re).cancel({revert:!0}):v(this,re).cancelRetry()),this.scheduleGc()),v(this,de).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||te(this,he,we).call(this,{type:"invalidate"})}fetch(t,r){var d,h,g;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(r!=null&&r.cancelRefetch))this.cancel({silent:!0});else if(v(this,re))return v(this,re).continueRetry(),v(this,re).promise}if(t&&this.setOptions(t),!this.options.queryFn){const p=this.observers.find(c=>c.options.queryFn);p&&this.setOptions(p.options)}const o=new AbortController,n=p=>{Object.defineProperty(p,"signal",{enumerable:!0,get:()=>(R(this,Le,!0),o.signal)})},a=()=>{const p=No(this.options,r),c={queryKey:this.queryKey,meta:this.meta};return n(c),R(this,Le,!1),this.options.persister?this.options.persister(p,c,this):p(c)},i={fetchOptions:r,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:a};n(i),(d=this.options.behavior)==null||d.onFetch(i,this),R(this,Xe,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((h=i.fetchOptions)==null?void 0:h.meta))&&te(this,he,we).call(this,{type:"fetch",meta:(g=i.fetchOptions)==null?void 0:g.meta});const l=p=>{var c,b,x,m;ir(p)&&p.silent||te(this,he,we).call(this,{type:"error",error:p}),ir(p)||((b=(c=v(this,de).config).onError)==null||b.call(c,p,this),(m=(x=v(this,de).config).onSettled)==null||m.call(x,this.state.data,p,this)),this.scheduleGc()};return R(this,re,ko({initialPromise:r==null?void 0:r.initialPromise,fn:i.fetchFn,abort:o.abort.bind(o),onSuccess:p=>{var c,b,x,m;if(p===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(p)}catch(f){l(f);return}(b=(c=v(this,de).config).onSuccess)==null||b.call(c,p,this),(m=(x=v(this,de).config).onSettled)==null||m.call(x,p,this.state.error,this),this.scheduleGc()},onError:l,onFail:(p,c)=>{te(this,he,we).call(this,{type:"failed",failureCount:p,error:c})},onPause:()=>{te(this,he,we).call(this,{type:"pause"})},onContinue:()=>{te(this,he,we).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),v(this,re).start()}},Qe=new WeakMap,Xe=new WeakMap,de=new WeakMap,re=new WeakMap,bt=new WeakMap,Le=new WeakMap,he=new WeakSet,we=function(t){const r=o=>{switch(t.type){case"failed":return{...o,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...o,fetchStatus:"paused"};case"continue":return{...o,fetchStatus:"fetching"};case"fetch":return{...o,...A0(o.data,this.options),fetchMeta:t.meta??null};case"success":return{...o,data:t.data,dataUpdateCount:o.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return ir(n)&&n.revert&&v(this,Xe)?{...v(this,Xe),fetchStatus:"idle"}:{...o,error:n,errorUpdateCount:o.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:o.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...o,isInvalidated:!0};case"setState":return{...o,...t.state}}};this.state=r(this.state),ne.batch(()=>{this.observers.forEach(o=>{o.onQueryUpdate()}),v(this,de).notify({query:this,type:"updated",action:t})})},vs);function A0(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:To(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function R0(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,r=t!==void 0,o=r?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?o??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}var xe,ys,z0=(ys=class extends Yt{constructor(t={}){super();_(this,xe);this.config=t,R(this,xe,new Map)}build(t,r,o){const n=r.queryKey,a=r.queryHash??Mr(n,r);let i=this.get(a);return i||(i=new M0({cache:this,queryKey:n,queryHash:a,options:t.defaultQueryOptions(r),state:o,defaultOptions:t.getQueryDefaults(n)}),this.add(i)),i}add(t){v(this,xe).has(t.queryHash)||(v(this,xe).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const r=v(this,xe).get(t.queryHash);r&&(t.destroy(),r===t&&v(this,xe).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){ne.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return v(this,xe).get(t)}getAll(){return[...v(this,xe).values()]}find(t){const r={exact:!0,...t};return this.getAll().find(o=>ns(r,o))}findAll(t={}){const r=this.getAll();return Object.keys(t).length>0?r.filter(o=>ns(t,o)):r}notify(t){ne.batch(()=>{this.listeners.forEach(r=>{r(t)})})}onFocus(){ne.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){ne.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},xe=new WeakMap,ys),be,oe,_e,ve,Te,ws,I0=(ws=class extends Po{constructor(t){super();_(this,ve);_(this,be);_(this,oe);_(this,_e);this.mutationId=t.mutationId,R(this,oe,t.mutationCache),R(this,be,[]),this.state=t.state||D0(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){v(this,be).includes(t)||(v(this,be).push(t),this.clearGcTimeout(),v(this,oe).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){R(this,be,v(this,be).filter(r=>r!==t)),this.scheduleGc(),v(this,oe).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){v(this,be).length||(this.state.status==="pending"?this.scheduleGc():v(this,oe).remove(this))}continue(){var t;return((t=v(this,_e))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var n,a,i,l,d,h,g,p,c,b,x,m,f,y,N,w,j,S,P,L;R(this,_e,ko({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(I,W)=>{te(this,ve,Te).call(this,{type:"failed",failureCount:I,error:W})},onPause:()=>{te(this,ve,Te).call(this,{type:"pause"})},onContinue:()=>{te(this,ve,Te).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>v(this,oe).canRun(this)}));const r=this.state.status==="pending",o=!v(this,_e).canStart();try{if(!r){te(this,ve,Te).call(this,{type:"pending",variables:t,isPaused:o}),await((a=(n=v(this,oe).config).onMutate)==null?void 0:a.call(n,t,this));const W=await((l=(i=this.options).onMutate)==null?void 0:l.call(i,t));W!==this.state.context&&te(this,ve,Te).call(this,{type:"pending",context:W,variables:t,isPaused:o})}const I=await v(this,_e).start();return await((h=(d=v(this,oe).config).onSuccess)==null?void 0:h.call(d,I,t,this.state.context,this)),await((p=(g=this.options).onSuccess)==null?void 0:p.call(g,I,t,this.state.context)),await((b=(c=v(this,oe).config).onSettled)==null?void 0:b.call(c,I,null,this.state.variables,this.state.context,this)),await((m=(x=this.options).onSettled)==null?void 0:m.call(x,I,null,t,this.state.context)),te(this,ve,Te).call(this,{type:"success",data:I}),I}catch(I){try{throw await((y=(f=v(this,oe).config).onError)==null?void 0:y.call(f,I,t,this.state.context,this)),await((w=(N=this.options).onError)==null?void 0:w.call(N,I,t,this.state.context)),await((S=(j=v(this,oe).config).onSettled)==null?void 0:S.call(j,void 0,I,this.state.variables,this.state.context,this)),await((L=(P=this.options).onSettled)==null?void 0:L.call(P,void 0,I,t,this.state.context)),I}finally{te(this,ve,Te).call(this,{type:"error",error:I})}}finally{v(this,oe).runNext(this)}}},be=new WeakMap,oe=new WeakMap,_e=new WeakMap,ve=new WeakSet,Te=function(t){const r=o=>{switch(t.type){case"failed":return{...o,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...o,isPaused:!0};case"continue":return{...o,isPaused:!1};case"pending":return{...o,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...o,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...o,data:void 0,error:t.error,failureCount:o.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=r(this.state),ne.batch(()=>{v(this,be).forEach(o=>{o.onMutationUpdate(t)}),v(this,oe).notify({mutation:this,type:"updated",action:t})})},ws);function D0(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var ie,vt,js,O0=(js=class extends Yt{constructor(t={}){super();_(this,ie);_(this,vt);this.config=t,R(this,ie,new Map),R(this,vt,Date.now())}build(t,r,o){const n=new I0({mutationCache:this,mutationId:++Nt(this,vt)._,options:t.defaultMutationOptions(r),state:o});return this.add(n),n}add(t){const r=Et(t),o=v(this,ie).get(r)??[];o.push(t),v(this,ie).set(r,o),this.notify({type:"added",mutation:t})}remove(t){var o;const r=Et(t);if(v(this,ie).has(r)){const n=(o=v(this,ie).get(r))==null?void 0:o.filter(a=>a!==t);n&&(n.length===0?v(this,ie).delete(r):v(this,ie).set(r,n))}this.notify({type:"removed",mutation:t})}canRun(t){var o;const r=(o=v(this,ie).get(Et(t)))==null?void 0:o.find(n=>n.state.status==="pending");return!r||r===t}runNext(t){var o;const r=(o=v(this,ie).get(Et(t)))==null?void 0:o.find(n=>n!==t&&n.state.isPaused);return(r==null?void 0:r.continue())??Promise.resolve()}clear(){ne.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...v(this,ie).values()].flat()}find(t){const r={exact:!0,...t};return this.getAll().find(o=>as(r,o))}findAll(t={}){return this.getAll().filter(r=>as(t,r))}notify(t){ne.batch(()=>{this.listeners.forEach(r=>{r(t)})})}resumePausedMutations(){const t=this.getAll().filter(r=>r.state.isPaused);return ne.batch(()=>Promise.all(t.map(r=>r.continue().catch(ue))))}},ie=new WeakMap,vt=new WeakMap,js);function Et(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function cs(e){return{onFetch:(t,r)=>{var g,p,c,b,x;const o=t.options,n=(c=(p=(g=t.fetchOptions)==null?void 0:g.meta)==null?void 0:p.fetchMore)==null?void 0:c.direction,a=((b=t.state.data)==null?void 0:b.pages)||[],i=((x=t.state.data)==null?void 0:x.pageParams)||[];let l={pages:[],pageParams:[]},d=0;const h=async()=>{let m=!1;const f=w=>{Object.defineProperty(w,"signal",{enumerable:!0,get:()=>(t.signal.aborted?m=!0:t.signal.addEventListener("abort",()=>{m=!0}),t.signal)})},y=No(t.options,t.fetchOptions),N=async(w,j,S)=>{if(m)return Promise.reject();if(j==null&&w.pages.length)return Promise.resolve(w);const P={queryKey:t.queryKey,pageParam:j,direction:S?"backward":"forward",meta:t.options.meta};f(P);const L=await y(P),{maxPages:I}=t.options,W=S?N0:C0;return{pages:W(w.pages,L,I),pageParams:W(w.pageParams,j,I)}};if(n&&a.length){const w=n==="backward",j=w?F0:ds,S={pages:a,pageParams:i},P=j(o,S);l=await N(S,P,w)}else{const w=e??a.length;do{const j=d===0?i[0]??o.initialPageParam:ds(o,l);if(d>0&&j==null)break;l=await N(l,j),d++}while(d<w)}return l};t.options.persister?t.fetchFn=()=>{var m,f;return(f=(m=t.options).persister)==null?void 0:f.call(m,h,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r)}:t.fetchFn=h}}}function ds(e,{pages:t,pageParams:r}){const o=t.length-1;return t.length>0?e.getNextPageParam(t[o],t,r[o],r):void 0}function F0(e,{pages:t,pageParams:r}){var o;return t.length>0?(o=e.getPreviousPageParam)==null?void 0:o.call(e,t[0],t,r[0],r):void 0}var X,Pe,Me,Je,Ze,Ae,et,tt,Cs,L0=(Cs=class{constructor(e={}){_(this,X);_(this,Pe);_(this,Me);_(this,Je);_(this,Ze);_(this,Ae);_(this,et);_(this,tt);R(this,X,e.queryCache||new z0),R(this,Pe,e.mutationCache||new O0),R(this,Me,e.defaultOptions||{}),R(this,Je,new Map),R(this,Ze,new Map),R(this,Ae,0)}mount(){Nt(this,Ae)._++,v(this,Ae)===1&&(R(this,et,So.subscribe(async e=>{e&&(await this.resumePausedMutations(),v(this,X).onFocus())})),R(this,tt,Dt.subscribe(async e=>{e&&(await this.resumePausedMutations(),v(this,X).onOnline())})))}unmount(){var e,t;Nt(this,Ae)._--,v(this,Ae)===0&&((e=v(this,et))==null||e.call(this),R(this,et,void 0),(t=v(this,tt))==null||t.call(this),R(this,tt,void 0))}isFetching(e){return v(this,X).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return v(this,Pe).findAll({...e,status:"pending"}).length}getQueryData(e){var r;const t=this.defaultQueryOptions({queryKey:e});return(r=v(this,X).get(t.queryHash))==null?void 0:r.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const r=this.defaultQueryOptions(e),o=v(this,X).build(this,r);return e.revalidateIfStale&&o.isStaleByTime(os(r.staleTime,o))&&this.prefetchQuery(r),Promise.resolve(t)}}getQueriesData(e){return v(this,X).findAll(e).map(({queryKey:t,state:r})=>{const o=r.data;return[t,o]})}setQueryData(e,t,r){const o=this.defaultQueryOptions({queryKey:e}),n=v(this,X).get(o.queryHash),a=n==null?void 0:n.state.data,i=x0(t,a);if(i!==void 0)return v(this,X).build(this,o).setData(i,{...r,manual:!0})}setQueriesData(e,t,r){return ne.batch(()=>v(this,X).findAll(e).map(({queryKey:o})=>[o,this.setQueryData(o,t,r)]))}getQueryState(e){var r;const t=this.defaultQueryOptions({queryKey:e});return(r=v(this,X).get(t.queryHash))==null?void 0:r.state}removeQueries(e){const t=v(this,X);ne.batch(()=>{t.findAll(e).forEach(r=>{t.remove(r)})})}resetQueries(e,t){const r=v(this,X),o={type:"active",...e};return ne.batch(()=>(r.findAll(e).forEach(n=>{n.reset()}),this.refetchQueries(o,t)))}cancelQueries(e={},t={}){const r={revert:!0,...t},o=ne.batch(()=>v(this,X).findAll(e).map(n=>n.cancel(r)));return Promise.all(o).then(ue).catch(ue)}invalidateQueries(e={},t={}){return ne.batch(()=>{if(v(this,X).findAll(e).forEach(o=>{o.invalidate()}),e.refetchType==="none")return Promise.resolve();const r={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(r,t)})}refetchQueries(e={},t){const r={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},o=ne.batch(()=>v(this,X).findAll(e).filter(n=>!n.isDisabled()).map(n=>{let a=n.fetch(void 0,r);return r.throwOnError||(a=a.catch(ue)),n.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(o).then(ue)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const r=v(this,X).build(this,t);return r.isStaleByTime(os(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(ue).catch(ue)}fetchInfiniteQuery(e){return e.behavior=cs(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(ue).catch(ue)}ensureInfiniteQueryData(e){return e.behavior=cs(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Dt.isOnline()?v(this,Pe).resumePausedMutations():Promise.resolve()}getQueryCache(){return v(this,X)}getMutationCache(){return v(this,Pe)}getDefaultOptions(){return v(this,Me)}setDefaultOptions(e){R(this,Me,e)}setQueryDefaults(e,t){v(this,Je).set(gt(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...v(this,Je).values()];let r={};return t.forEach(o=>{xt(e,o.queryKey)&&(r={...r,...o.defaultOptions})}),r}setMutationDefaults(e,t){v(this,Ze).set(gt(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...v(this,Ze).values()];let r={};return t.forEach(o=>{xt(e,o.mutationKey)&&(r={...r,...o.defaultOptions})}),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...v(this,Me).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Mr(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===Ar&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...v(this,Me).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){v(this,X).clear(),v(this,Pe).clear()}},X=new WeakMap,Pe=new WeakMap,Me=new WeakMap,Je=new WeakMap,Ze=new WeakMap,Ae=new WeakMap,et=new WeakMap,tt=new WeakMap,Cs),_0=u.createContext(void 0),B0=({client:e,children:t})=>(u.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),s.jsx(_0.Provider,{value:e,children:t}));const V0=({onBootComplete:e})=>{const[t,r]=u.useState(0);return u.useEffect(()=>{const o=setInterval(()=>{r(n=>n>=100?(clearInterval(o),setTimeout(()=>{e()},500),100):n+2)},20);return()=>clearInterval(o)},[e]),s.jsxs("div",{className:"h-screen w-screen bg-black flex flex-col items-center justify-center text-white",children:[s.jsxs("div",{className:"mb-16 flex flex-col items-center",children:[s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"w-8 h-12 bg-red-500 rounded-l-lg"}),s.jsx("div",{className:"w-8 h-12 bg-orange-500"}),s.jsx("div",{className:"w-8 h-12 bg-blue-500"}),s.jsx("div",{className:"w-8 h-12 bg-green-500 rounded-r-lg"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-lg text-gray-300 mb-1",children:"Microsoft"}),s.jsxs("div",{className:"text-3xl font-bold text-white mb-1",children:["Windows ",s.jsx("span",{className:"text-red-500",children:"XP"})]}),s.jsx("div",{className:"text-sm text-gray-400",children:"Professional"})]})]}),s.jsx("div",{className:"w-64 mb-8",children:s.jsx("div",{className:"bg-gray-800 h-4 rounded-sm border border-gray-600 overflow-hidden",children:s.jsx("div",{className:"h-full bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 transition-all duration-100 ease-out relative",style:{width:`${t}%`},children:s.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"})})})}),s.jsxs("div",{className:"text-sm text-gray-400",children:[t<30&&"Loading Windows...",t>=30&&t<60&&"Starting services...",t>=60&&t<90&&"Preparing desktop...",t>=90&&"Welcome"]})]})};var Rr="Popover",[Mo,u1]=_t(Rr,[Vt]),wt=Vt(),[H0,Re]=Mo(Rr),Ao=e=>{const{__scopePopover:t,children:r,open:o,defaultOpen:n,onOpenChange:a,modal:i=!1}=e,l=wt(t),d=u.useRef(null),[h,g]=u.useState(!1),[p=!1,c]=jr({prop:o,defaultProp:n,onChange:a});return s.jsx(In,{...l,children:s.jsx(H0,{scope:t,contentId:Dn(),triggerRef:d,open:p,onOpenChange:c,onOpenToggle:u.useCallback(()=>c(b=>!b),[c]),hasCustomAnchor:h,onCustomAnchorAdd:u.useCallback(()=>g(!0),[]),onCustomAnchorRemove:u.useCallback(()=>g(!1),[]),modal:i,children:r})})};Ao.displayName=Rr;var Ro="PopoverAnchor",W0=u.forwardRef((e,t)=>{const{__scopePopover:r,...o}=e,n=Re(Ro,r),a=wt(r),{onCustomAnchorAdd:i,onCustomAnchorRemove:l}=n;return u.useEffect(()=>(i(),()=>l()),[i,l]),s.jsx(Cr,{...a,...o,ref:t})});W0.displayName=Ro;var zo="PopoverTrigger",Io=u.forwardRef((e,t)=>{const{__scopePopover:r,...o}=e,n=Re(zo,r),a=wt(r),i=me(t,n.triggerRef),l=s.jsx(se.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":_o(n.open),...o,ref:i,onClick:$(e.onClick,n.onOpenToggle)});return n.hasCustomAnchor?l:s.jsx(Cr,{asChild:!0,...a,children:l})});Io.displayName=zo;var zr="PopoverPortal",[$0,G0]=Mo(zr,{forceMount:void 0}),Do=e=>{const{__scopePopover:t,forceMount:r,children:o,container:n}=e,a=Re(zr,t);return s.jsx($0,{scope:t,forceMount:r,children:s.jsx(Bt,{present:r||a.open,children:s.jsx(Ss,{asChild:!0,container:n,children:o})})})};Do.displayName=zr;var st="PopoverContent",Oo=u.forwardRef((e,t)=>{const r=G0(st,e.__scopePopover),{forceMount:o=r.forceMount,...n}=e,a=Re(st,e.__scopePopover);return s.jsx(Bt,{present:o||a.open,children:a.modal?s.jsx(q0,{...n,ref:t}):s.jsx(U0,{...n,ref:t})})});Oo.displayName=st;var q0=u.forwardRef((e,t)=>{const r=Re(st,e.__scopePopover),o=u.useRef(null),n=me(t,o),a=u.useRef(!1);return u.useEffect(()=>{const i=o.current;if(i)return Mn(i)},[]),s.jsx(An,{as:Ps,allowPinchZoom:!0,children:s.jsx(Fo,{...e,ref:n,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:$(e.onCloseAutoFocus,i=>{var l;i.preventDefault(),a.current||(l=r.triggerRef.current)==null||l.focus()}),onPointerDownOutside:$(e.onPointerDownOutside,i=>{const l=i.detail.originalEvent,d=l.button===0&&l.ctrlKey===!0,h=l.button===2||d;a.current=h},{checkForDefaultPrevented:!1}),onFocusOutside:$(e.onFocusOutside,i=>i.preventDefault(),{checkForDefaultPrevented:!1})})})}),U0=u.forwardRef((e,t)=>{const r=Re(st,e.__scopePopover),o=u.useRef(!1),n=u.useRef(!1);return s.jsx(Fo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var i,l;(i=e.onCloseAutoFocus)==null||i.call(e,a),a.defaultPrevented||(o.current||(l=r.triggerRef.current)==null||l.focus(),a.preventDefault()),o.current=!1,n.current=!1},onInteractOutside:a=>{var d,h;(d=e.onInteractOutside)==null||d.call(e,a),a.defaultPrevented||(o.current=!0,a.detail.originalEvent.type==="pointerdown"&&(n.current=!0));const i=a.target;((h=r.triggerRef.current)==null?void 0:h.contains(i))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&n.current&&a.preventDefault()}})}),Fo=u.forwardRef((e,t)=>{const{__scopePopover:r,trapFocus:o,onOpenAutoFocus:n,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:l,onPointerDownOutside:d,onFocusOutside:h,onInteractOutside:g,...p}=e,c=Re(st,r),b=wt(r);return Rn(),s.jsx(zn,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:n,onUnmountAutoFocus:a,children:s.jsx(Ts,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:g,onEscapeKeyDown:l,onPointerDownOutside:d,onFocusOutside:h,onDismiss:()=>c.onOpenChange(!1),children:s.jsx(Es,{"data-state":_o(c.open),role:"dialog",id:c.contentId,...b,...p,ref:t,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),Lo="PopoverClose",Y0=u.forwardRef((e,t)=>{const{__scopePopover:r,...o}=e,n=Re(Lo,r);return s.jsx(se.button,{type:"button",...o,ref:t,onClick:$(e.onClick,()=>n.onOpenChange(!1))})});Y0.displayName=Lo;var K0="PopoverArrow",Q0=u.forwardRef((e,t)=>{const{__scopePopover:r,...o}=e,n=wt(r);return s.jsx(ks,{...n,...o,ref:t})});Q0.displayName=K0;function _o(e){return e?"open":"closed"}var X0=Ao,J0=Io,Z0=Do,Bo=Oo;const lr=X0,cr=J0,zt=u.forwardRef(({className:e,align:t="center",sideOffset:r=4,...o},n)=>s.jsx(Z0,{children:s.jsx(Bo,{ref:n,align:t,sideOffset:r,className:fe("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...o})}));zt.displayName=Bo.displayName;function Vo(e,[t,r]){return Math.min(r,Math.max(t,e))}function el(e){const t=u.useRef({value:e,previous:e});return u.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var Ho=["PageUp","PageDown"],Wo=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],$o={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},nt="Slider",[vr,tl,rl]=Ns(nt),[Go,h1]=_t(nt,[rl]),[sl,Qt]=Go(nt),qo=u.forwardRef((e,t)=>{const{name:r,min:o=0,max:n=100,step:a=1,orientation:i="horizontal",disabled:l=!1,minStepsBetweenThumbs:d=0,defaultValue:h=[o],value:g,onValueChange:p=()=>{},onValueCommit:c=()=>{},inverted:b=!1,form:x,...m}=e,f=u.useRef(new Set),y=u.useRef(0),w=i==="horizontal"?ol:nl,[j=[],S]=jr({prop:g,defaultProp:h,onChange:O=>{var Z;(Z=[...f.current][y.current])==null||Z.focus(),p(O)}}),P=u.useRef(j);function L(O){const k=dl(j,O);Q(O,k)}function I(O){Q(O,y.current)}function W(){const O=P.current[y.current];j[y.current]!==O&&c(j)}function Q(O,k,{commit:Z}={commit:!1}){const T=fl(a),G=pl(Math.round((O-o)/a)*a+o,T),B=Vo(G,[o,n]);S((J=[])=>{const E=ll(J,B,k);if(ml(E,d*a)){y.current=E.indexOf(B);const U=String(E)!==String(J);return U&&Z&&c(E),U?E:J}else return J})}return s.jsx(sl,{scope:e.__scopeSlider,name:r,disabled:l,min:o,max:n,valueIndexToChangeRef:y,thumbs:f.current,values:j,orientation:i,form:x,children:s.jsx(vr.Provider,{scope:e.__scopeSlider,children:s.jsx(vr.Slot,{scope:e.__scopeSlider,children:s.jsx(w,{"aria-disabled":l,"data-disabled":l?"":void 0,...m,ref:t,onPointerDown:$(m.onPointerDown,()=>{l||(P.current=j)}),min:o,max:n,inverted:b,onSlideStart:l?void 0:L,onSlideMove:l?void 0:I,onSlideEnd:l?void 0:W,onHomeKeyDown:()=>!l&&Q(o,0,{commit:!0}),onEndKeyDown:()=>!l&&Q(n,j.length-1,{commit:!0}),onStepKeyDown:({event:O,direction:k})=>{if(!l){const G=Ho.includes(O.key)||O.shiftKey&&Wo.includes(O.key)?10:1,B=y.current,J=j[B],E=a*G*k;Q(J+E,B,{commit:!0})}}})})})})});qo.displayName=nt;var[Uo,Yo]=Go(nt,{startEdge:"left",endEdge:"right",size:"width",direction:1}),ol=u.forwardRef((e,t)=>{const{min:r,max:o,dir:n,inverted:a,onSlideStart:i,onSlideMove:l,onSlideEnd:d,onStepKeyDown:h,...g}=e,[p,c]=u.useState(null),b=me(t,w=>c(w)),x=u.useRef(),m=On(n),f=m==="ltr",y=f&&!a||!f&&a;function N(w){const j=x.current||p.getBoundingClientRect(),S=[0,j.width],L=Ir(S,y?[r,o]:[o,r]);return x.current=j,L(w-j.left)}return s.jsx(Uo,{scope:e.__scopeSlider,startEdge:y?"left":"right",endEdge:y?"right":"left",direction:y?1:-1,size:"width",children:s.jsx(Ko,{dir:m,"data-orientation":"horizontal",...g,ref:b,style:{...g.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:w=>{const j=N(w.clientX);i==null||i(j)},onSlideMove:w=>{const j=N(w.clientX);l==null||l(j)},onSlideEnd:()=>{x.current=void 0,d==null||d()},onStepKeyDown:w=>{const S=$o[y?"from-left":"from-right"].includes(w.key);h==null||h({event:w,direction:S?-1:1})}})})}),nl=u.forwardRef((e,t)=>{const{min:r,max:o,inverted:n,onSlideStart:a,onSlideMove:i,onSlideEnd:l,onStepKeyDown:d,...h}=e,g=u.useRef(null),p=me(t,g),c=u.useRef(),b=!n;function x(m){const f=c.current||g.current.getBoundingClientRect(),y=[0,f.height],w=Ir(y,b?[o,r]:[r,o]);return c.current=f,w(m-f.top)}return s.jsx(Uo,{scope:e.__scopeSlider,startEdge:b?"bottom":"top",endEdge:b?"top":"bottom",size:"height",direction:b?1:-1,children:s.jsx(Ko,{"data-orientation":"vertical",...h,ref:p,style:{...h.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:m=>{const f=x(m.clientY);a==null||a(f)},onSlideMove:m=>{const f=x(m.clientY);i==null||i(f)},onSlideEnd:()=>{c.current=void 0,l==null||l()},onStepKeyDown:m=>{const y=$o[b?"from-bottom":"from-top"].includes(m.key);d==null||d({event:m,direction:y?-1:1})}})})}),Ko=u.forwardRef((e,t)=>{const{__scopeSlider:r,onSlideStart:o,onSlideMove:n,onSlideEnd:a,onHomeKeyDown:i,onEndKeyDown:l,onStepKeyDown:d,...h}=e,g=Qt(nt,r);return s.jsx(se.span,{...h,ref:t,onKeyDown:$(e.onKeyDown,p=>{p.key==="Home"?(i(p),p.preventDefault()):p.key==="End"?(l(p),p.preventDefault()):Ho.concat(Wo).includes(p.key)&&(d(p),p.preventDefault())}),onPointerDown:$(e.onPointerDown,p=>{const c=p.target;c.setPointerCapture(p.pointerId),p.preventDefault(),g.thumbs.has(c)?c.focus():o(p)}),onPointerMove:$(e.onPointerMove,p=>{p.target.hasPointerCapture(p.pointerId)&&n(p)}),onPointerUp:$(e.onPointerUp,p=>{const c=p.target;c.hasPointerCapture(p.pointerId)&&(c.releasePointerCapture(p.pointerId),a(p))})})}),Qo="SliderTrack",Xo=u.forwardRef((e,t)=>{const{__scopeSlider:r,...o}=e,n=Qt(Qo,r);return s.jsx(se.span,{"data-disabled":n.disabled?"":void 0,"data-orientation":n.orientation,...o,ref:t})});Xo.displayName=Qo;var yr="SliderRange",Jo=u.forwardRef((e,t)=>{const{__scopeSlider:r,...o}=e,n=Qt(yr,r),a=Yo(yr,r),i=u.useRef(null),l=me(t,i),d=n.values.length,h=n.values.map(c=>en(c,n.min,n.max)),g=d>1?Math.min(...h):0,p=100-Math.max(...h);return s.jsx(se.span,{"data-orientation":n.orientation,"data-disabled":n.disabled?"":void 0,...o,ref:l,style:{...e.style,[a.startEdge]:g+"%",[a.endEdge]:p+"%"}})});Jo.displayName=yr;var wr="SliderThumb",Zo=u.forwardRef((e,t)=>{const r=tl(e.__scopeSlider),[o,n]=u.useState(null),a=me(t,l=>n(l)),i=u.useMemo(()=>o?r().findIndex(l=>l.ref.current===o):-1,[r,o]);return s.jsx(al,{...e,ref:a,index:i})}),al=u.forwardRef((e,t)=>{const{__scopeSlider:r,index:o,name:n,...a}=e,i=Qt(wr,r),l=Yo(wr,r),[d,h]=u.useState(null),g=me(t,N=>h(N)),p=d?i.form||!!d.closest("form"):!0,c=Fn(d),b=i.values[o],x=b===void 0?0:en(b,i.min,i.max),m=cl(o,i.values.length),f=c==null?void 0:c[l.size],y=f?ul(f,x,l.direction):0;return u.useEffect(()=>{if(d)return i.thumbs.add(d),()=>{i.thumbs.delete(d)}},[d,i.thumbs]),s.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[l.startEdge]:`calc(${x}% + ${y}px)`},children:[s.jsx(vr.ItemSlot,{scope:e.__scopeSlider,children:s.jsx(se.span,{role:"slider","aria-label":e["aria-label"]||m,"aria-valuemin":i.min,"aria-valuenow":b,"aria-valuemax":i.max,"aria-orientation":i.orientation,"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,tabIndex:i.disabled?void 0:0,...a,ref:g,style:b===void 0?{display:"none"}:e.style,onFocus:$(e.onFocus,()=>{i.valueIndexToChangeRef.current=o})})}),p&&s.jsx(il,{name:n??(i.name?i.name+(i.values.length>1?"[]":""):void 0),form:i.form,value:b},o)]})});Zo.displayName=wr;var il=e=>{const{value:t,...r}=e,o=u.useRef(null),n=el(t);return u.useEffect(()=>{const a=o.current,i=window.HTMLInputElement.prototype,d=Object.getOwnPropertyDescriptor(i,"value").set;if(n!==t&&d){const h=new Event("input",{bubbles:!0});d.call(a,t),a.dispatchEvent(h)}},[n,t]),s.jsx("input",{style:{display:"none"},...r,ref:o,defaultValue:t})};function ll(e=[],t,r){const o=[...e];return o[r]=t,o.sort((n,a)=>n-a)}function en(e,t,r){const a=100/(r-t)*(e-t);return Vo(a,[0,100])}function cl(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function dl(e,t){if(e.length===1)return 0;const r=e.map(n=>Math.abs(n-t)),o=Math.min(...r);return r.indexOf(o)}function ul(e,t,r){const o=e/2,a=Ir([0,50],[0,o]);return(o-a(t)*r)*r}function hl(e){return e.slice(0,-1).map((t,r)=>e[r+1]-t)}function ml(e,t){if(t>0){const r=hl(e);return Math.min(...r)>=t}return!0}function Ir(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const o=(t[1]-t[0])/(e[1]-e[0]);return t[0]+o*(r-e[0])}}function fl(e){return(String(e).split(".")[1]||"").length}function pl(e,t){const r=Math.pow(10,t);return Math.round(e*r)/r}var tn=qo,gl=Xo,xl=Jo,bl=Zo;const rn=u.forwardRef(({className:e,...t},r)=>s.jsxs(tn,{ref:r,className:fe("relative flex w-full touch-none select-none items-center data-[orientation=vertical]:h-full data-[orientation=vertical]:flex-col data-[orientation=vertical]:w-5",e),...t,children:[s.jsx(gl,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary data-[orientation=vertical]:h-full data-[orientation=vertical]:w-2",children:s.jsx(xl,{className:"absolute h-full bg-primary data-[orientation=vertical]:w-full"})}),s.jsx(bl,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));rn.displayName=tn.displayName;const ut=({label:e,value:t,onValueChange:r,disabled:o=!1})=>{const n=e.split(" ");return s.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[s.jsx("div",{className:"h-32",children:s.jsx(rn,{orientation:"vertical",value:t,onValueChange:r,max:100,step:1,disabled:o})}),s.jsxs("label",{className:"select-none text-center text-[10px] leading-tight w-12 sm:w-14",children:[n[0],s.jsx("br",{}),n.slice(1).join(" ")]})]})},vl=({onClose:e})=>{const[t,r]=u.useState([80]),[o,n]=u.useState([90]),[a,i]=u.useState([95]),[l,d]=u.useState([75]),[h,g]=u.useState([100]);return s.jsxs("div",{className:"w-[90vw] max-w-96 bg-[#ECE9D8] border border-gray-400 rounded-sm p-1 font-tahoma text-xs shadow-lg text-black",style:{fontFamily:"Tahoma, sans-serif",boxShadow:"2px 2px 4px rgba(0,0,0,0.5), inset 1px 1px 0px #FFF, inset -1px -1px 0px #848278",borderTop:"1px solid #FFFFFF",borderLeft:"1px solid #FFFFFF",borderRightColor:"#A0A0A0",borderBottomColor:"#A0A0A0",background:"#DFDDCF"},onClick:p=>p.stopPropagation(),children:[s.jsxs("div",{className:"flex items-center justify-between mb-3 px-1 py-0.5 bg-gradient-to-r from-[#0058E0] to-[#3891F8]",children:[s.jsx("span",{className:"font-bold text-white select-none",children:"Skill Mixer"}),e&&s.jsx("button",{onClick:e,className:"bg-[#E2614A] hover:bg-[#F0715A] w-4 h-4 flex items-center justify-center rounded-sm",style:{borderTop:"1px solid #FFF",borderLeft:"1px solid #FFF",borderRight:"1px solid #000",borderBottom:"1px solid #000"},children:s.jsx(yt,{size:10,className:"text-white",strokeWidth:2.5})})]}),s.jsxs("div",{className:"flex justify-around px-3 pb-3 pt-1",children:[s.jsx(ut,{label:"Enthusiasm Level",value:t,onValueChange:r}),s.jsx(ut,{label:"Collaboration Skills",value:o,onValueChange:n}),s.jsx(ut,{label:"Problem Solving",value:a,onValueChange:i}),s.jsx(ut,{label:"Learning Curve",value:l,onValueChange:d}),s.jsx(ut,{label:"Coffee Dependency",value:h,onValueChange:g,disabled:!0})]})]})},yl=()=>{const r={Junior:-75,Mid:0,Senior:75}["Senior"];return s.jsxs("div",{className:"relative w-48 h-24 flex items-center justify-center",children:[s.jsx("div",{className:"absolute w-full h-full border-4 border-gray-400 rounded-t-full border-b-0 bg-[#F5F5F5]",style:{boxSizing:"border-box"}}),s.jsx("div",{className:"absolute bottom-1 left-2 text-[10px] font-bold text-gray-700 select-none",children:"Junior"}),s.jsx("div",{className:"absolute top-1 left-1/2 -translate-x-1/2 text-[10px] font-bold text-gray-700 select-none",children:"Mid"}),s.jsx("div",{className:"absolute bottom-1 right-2 text-[10px] font-bold text-gray-700 select-none",children:"Senior"}),s.jsx("div",{className:"absolute bottom-0 left-1/2 h-[88px] w-0.5 origin-bottom transition-transform duration-500 ease-in-out",style:{transform:`translateX(-50%) rotate(${r}deg)`},children:s.jsx("div",{className:"w-full h-full bg-red-600"})}),s.jsx("div",{className:"absolute bottom-[-8px] left-1/2 -translate-x-1/2 w-4 h-4 bg-gray-600 rounded-full border-2 border-gray-400 z-10"})]})},wl=({onClose:e})=>s.jsxs("div",{className:"w-[80vw] max-w-64 bg-[#ECE9D8] border border-gray-400 rounded-sm p-1 font-tahoma text-xs shadow-lg text-black",style:{fontFamily:"Tahoma, sans-serif",boxShadow:"2px 2px 4px rgba(0,0,0,0.5), inset 1px 1px 0px #FFF, inset -1px -1px 0px #848278",borderTop:"1px solid #FFFFFF",borderLeft:"1px solid #FFFFFF",borderRightColor:"#A0A0A0",borderBottomColor:"#A0A0A0",background:"#DFDDCF"},onClick:t=>t.stopPropagation(),children:[s.jsxs("div",{className:"flex items-center justify-between mb-2 px-1 py-0.5 bg-gradient-to-r from-[#0058E0] to-[#3891F8]",children:[s.jsx("span",{className:"font-bold text-white select-none",children:"Experience Level Status"}),e&&s.jsx("button",{onClick:e,className:"bg-[#E2614A] hover:bg-[#F0715A] w-4 h-4 flex items-center justify-center rounded-sm",style:{borderTop:"1px solid #FFF",borderLeft:"1px solid #FFF",borderRight:"1px solid #000",borderBottom:"1px solid #000"},children:s.jsx(yt,{size:10,className:"text-white",strokeWidth:2.5})})]}),s.jsxs("div",{className:"flex flex-col items-center justify-center px-3 pb-3 pt-1",children:[s.jsx(yl,{}),s.jsx("p",{className:"mt-4 text-center select-none",children:"Connection Speed: Senior Level"}),s.jsx("p",{className:"text-center text-gray-600 text-[10px] select-none",children:"Max throughput achieved."})]})]}),jl=Zs("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Dr=u.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...n},a)=>{const i=o?Ps:"button";return s.jsx(i,{className:fe(jl({variant:t,size:r,className:e})),ref:a,...n})});Dr.displayName="Button";function Cl(e,t=[]){let r=[];function o(a,i){const l=u.createContext(i),d=r.length;r=[...r,i];function h(p){const{scope:c,children:b,...x}=p,m=(c==null?void 0:c[e][d])||l,f=u.useMemo(()=>x,Object.values(x));return s.jsx(m.Provider,{value:f,children:b})}function g(p,c){const b=(c==null?void 0:c[e][d])||l,x=u.useContext(b);if(x)return x;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${a}\``)}return h.displayName=a+"Provider",[h,g]}const n=()=>{const a=r.map(i=>u.createContext(i));return function(l){const d=(l==null?void 0:l[e])||a;return u.useMemo(()=>({[`__scope${e}`]:{...l,[e]:d}}),[l,d])}};return n.scopeName=e,[o,Nl(n,...t)]}function Nl(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const o=e.map(n=>({useScope:n(),scopeName:n.scopeName}));return function(a){const i=o.reduce((l,{useScope:d,scopeName:h})=>{const p=d(a)[`__scope${h}`];return{...l,...p}},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}var Or="Progress",Fr=100,[Sl,m1]=Cl(Or),[Tl,El]=Sl(Or),sn=u.forwardRef((e,t)=>{const{__scopeProgress:r,value:o=null,max:n,getValueLabel:a=kl,...i}=e;(n||n===0)&&!us(n)&&console.error(Pl(`${n}`,"Progress"));const l=us(n)?n:Fr;o!==null&&!hs(o,l)&&console.error(Ml(`${o}`,"Progress"));const d=hs(o,l)?o:null,h=Ot(d)?a(d,l):void 0;return s.jsx(Tl,{scope:r,value:d,max:l,children:s.jsx(se.div,{"aria-valuemax":l,"aria-valuemin":0,"aria-valuenow":Ot(d)?d:void 0,"aria-valuetext":h,role:"progressbar","data-state":an(d,l),"data-value":d??void 0,"data-max":l,...i,ref:t})})});sn.displayName=Or;var on="ProgressIndicator",nn=u.forwardRef((e,t)=>{const{__scopeProgress:r,...o}=e,n=El(on,r);return s.jsx(se.div,{"data-state":an(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...o,ref:t})});nn.displayName=on;function kl(e,t){return`${Math.round(e/t*100)}%`}function an(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function Ot(e){return typeof e=="number"}function us(e){return Ot(e)&&!isNaN(e)&&e>0}function hs(e,t){return Ot(e)&&!isNaN(e)&&e<=t&&e>=0}function Pl(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Fr}\`.`}function Ml(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Fr} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var ln=sn,Al=nn;const cn=u.forwardRef(({className:e,value:t,...r},o)=>s.jsx(ln,{ref:o,className:fe("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:s.jsx(Al,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));cn.displayName=ln.displayName;const Rl=({onClose:e})=>{const[t,r]=u.useState(!1),[o,n]=u.useState(0),[a,i]=u.useState(!1);u.useEffect(()=>{let d;return t&&o<100?d=setTimeout(()=>{n(h=>Math.min(100,h+1))},50):t&&o>=100&&(r(!1),i(!0)),()=>clearTimeout(d)},[t,o]);const l=()=>{n(0),i(!1),r(!0)};return s.jsxs("div",{className:"w-[95vw] max-w-md bg-[#ECE9D8] border border-gray-400 rounded-sm p-1 font-tahoma text-xs shadow-lg text-black",style:{fontFamily:"Tahoma, sans-serif",boxShadow:"2px 2px 4px rgba(0,0,0,0.5), inset 1px 1px 0px #FFF, inset -1px -1px 0px #848278",borderTop:"1px solid #FFFFFF",borderLeft:"1px solid #FFFFFF",borderRightColor:"#A0A0A0",borderBottomColor:"#A0A0A0",background:"#DFDDCF"},onClick:d=>d.stopPropagation(),children:[s.jsxs("div",{className:"flex items-center justify-between mb-2 px-1 py-0.5 bg-gradient-to-r from-[#0058E0] to-[#3891F8]",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(pt,{size:14,className:"text-white"}),s.jsx("span",{className:"font-bold text-white select-none",children:"Security Suite - Code Guardian"})]}),e&&s.jsx("button",{onClick:e,className:"bg-[#E2614A] hover:bg-[#F0715A] w-4 h-4 flex items-center justify-center rounded-sm",style:{borderTop:"1px solid #FFF",borderLeft:"1px solid #FFF",borderRight:"1px solid #000",borderBottom:"1px solid #000"},children:s.jsx(yt,{size:10,className:"text-white",strokeWidth:2.5})})]}),s.jsxs("div",{className:"p-2 grid grid-cols-1 md:grid-cols-2 gap-2",children:[s.jsx("div",{className:"space-y-2",children:s.jsxs("div",{className:"border p-2 bg-gray-50 rounded-sm h-full",children:[s.jsxs("h3",{className:"font-bold mb-2 flex items-center gap-1",children:[s.jsx(pt,{className:"text-green-600",size:16})," System Protection Status"]}),s.jsx("p",{className:"text-green-700 font-semibold",children:"Code quality: Protected"}),s.jsx("p",{className:"text-xs text-gray-600 mt-1",children:"Your codebase is clean and follows best practices. No legacy code detected."})]})}),s.jsx("div",{className:"space-y-2",children:s.jsxs("div",{className:"border p-2 bg-gray-50 rounded-sm h-full flex flex-col",children:[s.jsx("h3",{className:"font-bold mb-2",children:"Full System Scan"}),s.jsxs("div",{className:"flex-grow",children:[!t&&!a&&s.jsx("p",{className:"text-xs text-gray-600 mb-2",children:"Scan through portfolio to find threats and bugs."}),t&&s.jsxs("div",{className:"space-y-2",children:[s.jsx(cn,{value:o,className:"h-4"}),s.jsxs("p",{className:"text-xs text-center",children:["Scanning... ",o,"%"]}),s.jsx("p",{className:"text-xs text-gray-500 truncate",children:"Checking: src/components/..."})]}),a&&s.jsxs("div",{className:"text-center p-2 bg-green-100 rounded",children:[s.jsx(pt,{className:"text-green-600 mx-auto",size:24}),s.jsx("p",{className:"font-bold text-green-700 mt-1",children:"Scan Complete"}),s.jsx("p",{className:"text-xs",children:"System clean - developer ready for deployment!"})]})]}),s.jsx(Dr,{onClick:l,disabled:t,className:"w-full mt-2",size:"sm",variant:"secondary",children:t?"Scanning...":"Start Full Scan"})]})})]}),s.jsxs("div",{className:"border-t mt-2 p-2 bg-gray-50 rounded-sm",children:[s.jsx("h3",{className:"font-bold mb-2",children:"Threat Detection"}),s.jsxs("div",{className:"text-xs space-y-1",children:[s.jsxs("p",{children:["Real-time scanning: ",s.jsx("span",{className:"text-green-600 font-semibold",children:"Active"})]}),s.jsx("p",{children:"Quarantined items: 0"}),s.jsxs("p",{children:["Security vulnerabilities: ",s.jsx("span",{className:"font-bold text-green-600",children:"0 found"})]})]})]}),s.jsx("div",{className:"border-t p-1 text-center text-[10px] text-gray-500",children:"Security definitions updated with latest best practices."})]})},zl=({openWindows:e,onStartClick:t,onWindowClick:r,onOpenWindow:o})=>{const[n,a]=u.useState(new Date),[i,l]=u.useState(!1),[d,h]=u.useState(!1),[g,p]=u.useState(!1);u.useEffect(()=>{const b=setInterval(()=>{a(new Date)},1e3);return()=>clearInterval(b)},[]);const c=b=>b.toLocaleTimeString("en-US",{hour12:!0,hour:"numeric",minute:"2-digit"});return s.jsxs("div",{className:"fixed bottom-0 left-0 right-0 h-10 xp-taskbar flex items-center z-50",children:[s.jsxs("button",{className:"h-full px-4 text-sm flex items-center space-x-2 font-bold text-white relative overflow-hidden group transition-all duration-75 xp-start-button-realistic",onClick:b=>{b.stopPropagation(),t()},style:{background:"linear-gradient(to bottom, #5cb85c 0%, #4cae4c 50%, #3e8e3e 100%)",border:"1px outset #5cb85c",borderRadius:"0 8px 8px 0",boxShadow:"inset 1px 1px 2px rgba(255,255,255,0.4), inset -1px -1px 2px rgba(0,0,0,0.2), 2px 2px 4px rgba(0,0,0,0.3)"},onMouseDown:b=>{b.currentTarget.style.border="1px inset #5cb85c",b.currentTarget.style.boxShadow="inset -1px -1px 2px rgba(255,255,255,0.4), inset 1px 1px 2px rgba(0,0,0,0.3)"},onMouseUp:b=>{b.currentTarget.style.border="1px outset #5cb85c",b.currentTarget.style.boxShadow="inset 1px 1px 2px rgba(255,255,255,0.4), inset -1px -1px 2px rgba(0,0,0,0.2), 2px 2px 4px rgba(0,0,0,0.3)"},onMouseLeave:b=>{b.currentTarget.style.border="1px outset #5cb85c",b.currentTarget.style.boxShadow="inset 1px 1px 2px rgba(255,255,255,0.4), inset -1px -1px 2px rgba(0,0,0,0.2), 2px 2px 4px rgba(0,0,0,0.3)"},children:[s.jsx("div",{className:"w-6 h-6 flex items-center justify-center",children:s.jsx("img",{src:"/windows-logo.png",alt:"Windows XP Logo",className:"w-5 h-5 drop-shadow-sm"})}),s.jsx("span",{className:"text-white font-bold drop-shadow-sm select-none",style:{fontFamily:"Tahoma, sans-serif",fontSize:"13px",textShadow:"1px 1px 1px rgba(0,0,0,0.5)"},children:"start"}),s.jsx("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-150 pointer-events-none",style:{background:"linear-gradient(to bottom, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0) 100%)",borderRadius:"0 8px 8px 0"}})]}),s.jsxs("div",{className:"hidden sm:flex items-center space-x-1 ml-2 pl-2 border-l border-blue-400",children:[s.jsx("button",{className:"p-1 hover:bg-blue-400 rounded",title:"My Projects",onClick:()=>o("projects","My Projects"),children:s.jsx("span",{className:"text-lg",children:"📁"})}),s.jsx("button",{className:"p-1 hover:bg-blue-400 rounded",title:"Contact Info",onClick:()=>o("contact","Contact Info"),children:s.jsx("span",{className:"text-lg",children:"📧"})})]}),s.jsx("div",{className:"flex-1 flex items-center space-x-1 ml-2 h-full",children:e.map(b=>s.jsx("button",{className:`h-full flex items-center px-2 sm:px-3 text-xs sm:text-sm text-white rounded border ${b.isMinimized?"bg-blue-600 border-blue-500":"bg-blue-500 border-blue-400"} hover:bg-blue-400 transition-colors max-w-24 sm:max-w-40 truncate`,onClick:()=>r(b.id),title:b.title,children:b.title},b.id))}),s.jsxs("div",{className:"flex items-center space-x-1 sm:space-x-2 mr-2 border-l border-blue-400 pl-1 sm:pl-2",children:[s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsxs(lr,{open:i,onOpenChange:l,children:[s.jsx(cr,{asChild:!0,children:s.jsx("button",{className:"text-white cursor-pointer hover:bg-blue-400 p-1 rounded focus:outline-none",children:s.jsx(Ba,{size:16})})}),s.jsx(zt,{className:"w-auto p-0 border-none bg-transparent shadow-none mb-1",side:"top",align:"end",sideOffset:5,children:s.jsx(vl,{onClose:()=>l(!1)})})]}),s.jsxs(lr,{open:d,onOpenChange:h,children:[s.jsx(cr,{asChild:!0,children:s.jsx("button",{className:"text-white cursor-pointer hover:bg-blue-400 p-1 rounded focus:outline-none",children:s.jsx(Aa,{size:16})})}),s.jsx(zt,{className:"w-auto p-0 border-none bg-transparent shadow-none mb-1",side:"top",align:"end",sideOffset:5,children:s.jsx(wl,{onClose:()=>h(!1)})})]}),s.jsxs(lr,{open:g,onOpenChange:p,children:[s.jsx(cr,{asChild:!0,children:s.jsx("button",{className:"text-white cursor-pointer hover:bg-blue-400 p-1 rounded focus:outline-none",children:s.jsx(pt,{size:16})})}),s.jsx(zt,{className:"w-auto p-0 border-none bg-transparent shadow-none mb-1",side:"top",align:"end",sideOffset:5,children:s.jsx(Rl,{onClose:()=>p(!1)})})]})]}),s.jsx("div",{className:"text-white text-xs font-medium",children:c(n)})]})]})},Il=({icon:e,onDoubleClick:t})=>{const[r,o]=u.useState(!1),[n,a]=u.useState(0),i=()=>{o(!0),a(l=>l+1),setTimeout(()=>{n===0&&o(!1)},300),n===1&&(t(),a(0),o(!1)),setTimeout(()=>{a(0)},300)};return s.jsxs("div",{className:`flex flex-col items-center w-20 p-2 cursor-pointer transition-all ${r?"bg-blue-500 bg-opacity-30":""}`,style:{outline:"none",border:"none"},onClick:i,children:[s.jsx("div",{className:"h-10 mb-1 flex items-center justify-center",children:s.jsx("span",{className:"text-3xl drop-shadow-lg",children:e.icon})}),s.jsx("div",{className:"text-white text-xs text-center leading-tight font-medium drop-shadow-lg",children:e.name})]})},Dl=({windowData:e,onUpdatePosition:t,onBringToFront:r,isMobile:o})=>{const[n,a]=u.useState(!1),[i,l]=u.useState({x:0,y:0}),{position:d,isMaximized:h}=e,g=u.useCallback(p=>{p.target.closest(".window-controls")||p.target.closest(".resize-handle")||h||o||(a(!0),l({x:p.clientX-d.x,y:p.clientY-d.y}),r())},[h,o,d.x,d.y,r]);return u.useEffect(()=>{const p=b=>{const x={x:b.clientX-i.x,y:b.clientY-i.y};x.x=Math.max(0,Math.min(x.x,globalThis.window.innerWidth-e.size.width)),x.y=Math.max(0,Math.min(x.y,globalThis.window.innerHeight-e.size.height-40)),t(x)},c=()=>{a(!1)};return n&&(document.addEventListener("mousemove",p),document.addEventListener("mouseup",c)),()=>{document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",c)}},[n,i,e.size,t]),{handleMouseDown:g}},Ol=({windowData:e,onUpdatePosition:t,onUpdateSize:r,onBringToFront:o,isMobile:n})=>{const[a,i]=u.useState(!1),[l,d]=u.useState(""),[h,g]=u.useState({x:0,y:0,width:0,height:0}),{size:p,position:c,isMaximized:b}=e,x=u.useCallback((m,f)=>{b||n||!r||(m.stopPropagation(),i(!0),d(f),g({x:m.clientX,y:m.clientY,width:p.width,height:p.height}),o())},[b,n,r,o,p.width,p.height]);return u.useEffect(()=>{const m=y=>{if(!a||!r)return;const N=y.clientX-h.x,w=y.clientY-h.y;let j=h.width,S=h.height,P=c.x,L=c.y;const I=300,W=200;if(l.includes("right")&&(j=Math.max(I,h.width+N),j=Math.min(j,globalThis.window.innerWidth-c.x)),l.includes("left")){const Q=h.width-N;Q>=I&&(j=Q,P=c.x+N,P<0&&(j+=P,P=0))}if(l.includes("bottom")&&(S=Math.max(W,h.height+w),S=Math.min(S,globalThis.window.innerHeight-c.y-40)),l.includes("top")){const Q=h.height-w;Q>=W&&(S=Q,L=c.y+w,L<0&&(S+=L,L=0))}r({width:j,height:S}),(P!==c.x||L!==c.y)&&t({x:P,y:L})},f=()=>{i(!1),d("")};return a&&(document.addEventListener("mousemove",m),document.addEventListener("mouseup",f)),()=>{document.removeEventListener("mousemove",m),document.removeEventListener("mouseup",f)}},[a,l,h,c,r,t]),{handleResizeStart:x}},Fl=({window:e,onClose:t,onMinimize:r,onMaximize:o,onBringToFront:n,onUpdatePosition:a,onUpdateSize:i,onRestore:l,isMobile:d})=>{const h=u.useRef(null),{handleMouseDown:g}=Dl({windowData:e,onUpdatePosition:a,onBringToFront:n,isMobile:d}),{handleResizeStart:p}=Ol({windowData:e,onUpdatePosition:a,onUpdateSize:i,onBringToFront:n,isMobile:d}),c=()=>{e.isMaximized&&l?l():o()},b=typeof e.content!="string",x=m=>({top:"n-resize",bottom:"s-resize",left:"w-resize",right:"e-resize","top-left":"nw-resize","top-right":"ne-resize","bottom-left":"sw-resize","bottom-right":"se-resize"})[m]||"default";return s.jsxs("div",{ref:h,className:"absolute xp-window bg-gray-100 animate-window-open",style:{left:e.position.x,top:e.position.y,width:e.size.width,height:e.size.height,zIndex:e.zIndex},onMouseDown:n,children:[!e.isMaximized&&!d&&s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"resize-handle absolute top-0 left-0 w-2 h-2 z-10",style:{cursor:x("top-left")},onMouseDown:m=>p(m,"top-left")}),s.jsx("div",{className:"resize-handle absolute top-0 right-0 w-2 h-2 z-10",style:{cursor:x("top-right")},onMouseDown:m=>p(m,"top-right")}),s.jsx("div",{className:"resize-handle absolute bottom-0 left-0 w-2 h-2 z-10",style:{cursor:x("bottom-left")},onMouseDown:m=>p(m,"bottom-left")}),s.jsx("div",{className:"resize-handle absolute bottom-0 right-0 w-2 h-2 z-10",style:{cursor:x("bottom-right")},onMouseDown:m=>p(m,"bottom-right")}),s.jsx("div",{className:"resize-handle absolute top-0 left-2 right-2 h-1 z-10",style:{cursor:x("top")},onMouseDown:m=>p(m,"top")}),s.jsx("div",{className:"resize-handle absolute bottom-0 left-2 right-2 h-1 z-10",style:{cursor:x("bottom")},onMouseDown:m=>p(m,"bottom")}),s.jsx("div",{className:"resize-handle absolute left-0 top-2 bottom-2 w-1 z-10",style:{cursor:x("left")},onMouseDown:m=>p(m,"left")}),s.jsx("div",{className:"resize-handle absolute right-0 top-2 bottom-2 w-1 z-10",style:{cursor:x("right")},onMouseDown:m=>p(m,"right")})]}),s.jsxs("div",{className:`xp-titlebar h-7 flex items-center justify-between px-2 select-none ${e.isMaximized||d?"cursor-default":"cursor-move"}`,onMouseDown:g,children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-white text-xs",children:e.icon}),s.jsx("span",{className:"text-white text-sm font-medium",children:e.title})]}),s.jsxs("div",{className:"window-controls flex items-center space-x-1",children:[s.jsx("button",{className:"w-5 h-4 bg-gray-300 hover:bg-gray-400 border border-gray-500 text-xs flex items-center justify-center",onClick:r,title:"Minimize",children:"_"}),!d&&s.jsx("button",{className:"w-5 h-4 bg-gray-300 hover:bg-gray-400 border border-gray-500 text-xs flex items-center justify-center",onClick:c,title:e.isMaximized?"Restore":"Maximize",children:e.isMaximized?"❐":"□"}),s.jsx("button",{className:"w-5 h-4 bg-red-500 hover:bg-red-600 border border-red-700 text-white text-xs flex items-center justify-center",onClick:t,title:"Close",children:"×"})]})]}),e.id!=="my_resume"&&s.jsxs("div",{className:"h-6 bg-gray-200 border-b border-gray-300 flex items-center px-2 text-xs",children:[s.jsx("span",{className:"hover:bg-blue-500 hover:text-white px-2 py-1 cursor-pointer",children:"File"}),s.jsx("span",{className:"hover:bg-blue-500 hover:text-white px-2 py-1 cursor-pointer",children:"Edit"}),s.jsx("span",{className:"hover:bg-blue-500 hover:text-white px-2 py-1 cursor-pointer",children:"View"}),s.jsx("span",{className:"hover:bg-blue-500 hover:text-white px-2 py-1 cursor-pointer",children:"Help"})]}),s.jsx("div",{className:"overflow-auto",style:{height:`calc(100% - ${e.id==="my_resume"?"1.75rem":"3.25rem"})`},children:b?e.content:s.jsx("div",{dangerouslySetInnerHTML:{__html:e.content}})})]})},Ll=u.memo(Fl),_l=()=>s.jsxs("div",{className:"bg-gradient-to-r from-[#2155C4] to-[#4D90FE] p-2 text-white flex items-center space-x-3",children:[s.jsx("div",{className:"w-12 h-12 bg-gray-200 border-2 border-white rounded-md flex items-center justify-center",children:s.jsx(_a,{size:36,className:"text-gray-700"})}),s.jsx("div",{className:"font-bold text-lg drop-shadow-sm",children:"John Smith"})]}),dr=({icon:e,name:t,subtext:r,bold:o,onClick:n})=>s.jsxs("button",{className:"w-full flex items-center space-x-3 px-3 py-1.5 hover:bg-[#316AC5] hover:text-white text-left text-sm rounded-sm transition-colors duration-150 group",onClick:n,children:[s.jsx("div",{className:"w-8 h-8 flex items-center justify-center",children:e}),s.jsxs("div",{className:"flex-1",children:[s.jsx("span",{className:`text-black group-hover:text-white ${o?"font-bold":""}`,children:t}),r&&s.jsx("div",{className:"text-xs text-gray-500 group-hover:text-gray-200",children:r})]})]}),kt=({icon:e,name:t,onClick:r})=>s.jsx("button",{onClick:r,className:"w-full flex items-center justify-between space-x-3 px-2 py-1.5 hover:bg-[#316AC5] hover:text-white text-left text-sm rounded-sm transition-colors duration-150 group",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-6 h-6 flex items-center justify-center",children:e}),s.jsx("span",{className:"text-black group-hover:text-white",children:t})]})}),Bl=()=>{const e=t=>{window.open(t,"_blank","noopener,noreferrer")};return s.jsx("div",{className:"absolute bottom-0 left-full w-[240px] bg-white border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl rounded-tr-lg rounded-br-lg animate-in fade-in duration-150",style:{fontFamily:"Tahoma, sans-serif"},onClick:t=>t.stopPropagation(),children:s.jsxs("div",{className:"p-1 space-y-0.5",children:[s.jsx(kt,{icon:s.jsx(Er,{size:20,className:"text-blue-700"}),name:"Browser Extension",onClick:()=>e("https://github.com/yourusername")}),s.jsx(kt,{icon:s.jsx(Fa,{size:20,className:"text-blue-700"}),name:"Mobile App",onClick:()=>e("https://github.com/yourusername")}),s.jsx(kt,{icon:s.jsx(La,{size:20,className:"text-blue-700"}),name:"Smart TV App",onClick:()=>e("https://github.com/yourusername")}),s.jsx("div",{className:"border-t border-gray-300 my-1"}),s.jsx(kt,{icon:s.jsx(pt,{size:20,className:"text-green-700"}),name:"Security Suite",onClick:()=>e("https://github.com/yourusername")})]})})},Vl=({menuItems:e,handleOpenWindow:t,showAllPrograms:r,setShowAllPrograms:o})=>{const n=e.reduce((d,h)=>(d[h.name.toLowerCase()]=h,d),{}),a=n.projects,i=n.contact,l=e.filter(d=>!["projects","contact"].includes(d.name.toLowerCase()));return s.jsxs("div",{className:"w-1/2 bg-white p-2 flex flex-col border-r border-gray-300",children:[s.jsxs("div",{className:"space-y-1",children:[s.jsx(dr,{icon:s.jsx(Er,{size:32}),name:"Internet",subtext:"Projects",bold:!0,onClick:()=>a&&t(a.id,a.name)}),s.jsx(dr,{icon:s.jsx(Gt,{size:32}),name:"E-mail",subtext:"Contact",bold:!0,onClick:()=>i&&t(i.id,i.name)}),s.jsx("div",{className:"border-t border-gray-300 my-1 !mt-2 !mb-2"}),l.map(d=>s.jsx(dr,{icon:s.jsx("span",{className:"text-2xl",children:d.icon}),name:d.name,onClick:()=>t(d.id,d.name)},d.id))]}),s.jsxs("div",{className:"!mt-auto pt-2 border-t border-gray-300 relative",onMouseLeave:()=>o(!1),children:[s.jsxs("button",{className:"w-full flex items-center justify-between px-3 py-1.5 hover:bg-[#316AC5] hover:text-white text-left text-sm rounded-sm group transition-colors duration-150",onMouseEnter:()=>o(!0),children:[s.jsx("div",{className:"flex items-center space-x-3",children:s.jsx("span",{className:"font-bold text-black group-hover:text-white",children:"All Programs"})}),s.jsx(xa,{size:20,className:"text-black group-hover:text-white"})]}),r&&s.jsx(Bl,{})]})]})},Ie=({icon:e,name:t,onClick:r})=>s.jsxs("button",{className:"w-full flex items-center space-x-3 px-3 py-1 hover:bg-[#316AC5] hover:text-white text-left text-sm rounded-sm transition-colors duration-150 group",onClick:r,children:[s.jsx("div",{className:"w-6 h-6 flex items-center justify-center",children:e}),s.jsx("span",{className:"font-semibold text-black group-hover:text-white",children:t})]}),Hl=({handleOpenWindow:e})=>s.jsxs("div",{className:"w-1/2 p-2 space-y-0.5",style:{backgroundColor:"#D4E1F4"},children:[s.jsx(Ie,{icon:s.jsx(De,{size:20,className:"text-blue-700"}),name:"My Documents",onClick:()=>e("my_documents","My Documents")}),s.jsx(Ie,{icon:s.jsx(to,{size:20,className:"text-blue-700"}),name:"My Pictures",onClick:()=>e("my_pictures","My Pictures")}),s.jsx(Ie,{icon:s.jsx(Ma,{size:20,className:"text-blue-700"}),name:"My Music",onClick:()=>e("my_music","Windows Media Player")}),s.jsx(Ie,{icon:s.jsx(ya,{size:20,className:"text-blue-700"}),name:"My Computer",onClick:()=>e("my_computer","My Computer")}),s.jsx("div",{className:"border-t border-[#A5B5D3] my-1 !mt-2 !mb-2"}),s.jsx(Ie,{icon:s.jsx(so,{size:20,className:"text-blue-700"}),name:"Control Panel",onClick:()=>e("control_panel","Control Panel")}),s.jsx("div",{className:"border-t border-[#A5B5D3] my-1 !mt-2 !mb-2"}),s.jsx(Ie,{icon:s.jsx(ba,{size:20,className:"text-blue-700"}),name:"Help and Support",onClick:()=>e("contact","Contact Info")}),s.jsx(Ie,{icon:s.jsx(Oa,{size:20,className:"text-blue-700"}),name:"Search"})]}),Wl=({onClose:e,onShutdown:t})=>s.jsxs("div",{className:"bg-gradient-to-r from-[#2155C4] to-[#4D90FE] p-1 flex justify-end items-center space-x-2 border-t border-t-[#769bce]",children:[s.jsxs("button",{onClick:()=>{t("logoff"),e()},className:"flex items-center space-x-2 text-white hover:opacity-80 px-2 py-1 rounded-sm text-sm transition-opacity",children:[s.jsx(ka,{size:20}),s.jsx("span",{children:"Log Off"})]}),s.jsxs("button",{className:"flex items-center space-x-2 text-white hover:opacity-80 px-2 py-1 rounded-sm text-sm transition-opacity",onClick:()=>{t("shutdown"),e()},children:[s.jsx(Da,{size:20}),s.jsx("span",{children:"Turn Off"})]})]}),$l=({onClose:e,onOpenWindow:t,onShutdown:r,menuItems:o})=>{const[n,a]=u.useState(!1),i=(l,d)=>{t(l,d),e()};return s.jsx("div",{className:"fixed bottom-10 left-0 z-50 flex items-end",children:s.jsx("div",{className:"relative",children:s.jsxs("div",{className:`w-[480px] border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl overflow-hidden ${n?"rounded-l-lg rounded-t-lg":"rounded-lg"}`,onClick:l=>l.stopPropagation(),style:{fontFamily:"Tahoma, sans-serif"},children:[s.jsx(_l,{}),s.jsxs("div",{className:"flex",children:[s.jsx(Vl,{menuItems:o,handleOpenWindow:i,showAllPrograms:n,setShowAllPrograms:a}),s.jsx(Hl,{handleOpenWindow:i})]}),s.jsx(Wl,{onClose:e,onShutdown:r})]})})})},Gl=({onComplete:e,type:t})=>{const[r,o]=u.useState(t==="logoff"?"contacts":"dialog");u.useEffect(()=>{if(t==="shutdown"){const l=setTimeout(()=>{o("thank-you")},2e3),d=setTimeout(()=>{o("fade")},4e3),h=setTimeout(()=>{o("contacts")},5e3);return()=>{clearTimeout(l),clearTimeout(d),clearTimeout(h)}}},[t]);const n=()=>{window.open("https://linkedin.com/in/your-profile","_blank")},a=()=>{window.open("mailto:<EMAIL>","_blank")},i=()=>{e&&e()};return s.jsxs("div",{className:"fixed inset-0 z-[9999] bg-black flex items-center justify-center",children:[r==="dialog"&&s.jsx("div",{className:"bg-gray-200 border-2 border-gray-400 shadow-2xl p-8 rounded animate-fade-in",children:s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-12 h-12 bg-blue-500 rounded flex items-center justify-center",children:s.jsx("span",{className:"text-white text-xl",children:"💻"})}),s.jsxs("div",{children:[s.jsx("h2",{className:"text-lg font-bold text-gray-800",children:"Windows"}),s.jsx("p",{className:"text-sm text-gray-600",children:"It's now safe to turn off your computer"})]})]})}),r==="thank-you"&&s.jsxs("div",{className:"text-center animate-fade-in",children:[s.jsx("h1",{className:"text-4xl font-bold text-white mb-4",children:"Thank You!"}),s.jsx("p",{className:"text-xl text-gray-300 mb-2",children:"Thank you for visiting John Smith's portfolio."}),s.jsx("p",{className:"text-lg text-gray-400",children:"Hope to connect soon!"})]}),r==="fade"&&s.jsx("div",{className:"w-full h-full bg-black animate-fade-in"}),r==="contacts"&&s.jsxs("div",{className:"text-center animate-fade-in",children:[s.jsx("p",{className:"text-gray-400 mb-8 text-lg",children:"Let's connect:"}),s.jsxs("div",{className:"flex justify-center space-x-8 mb-8",children:[s.jsxs("button",{onClick:n,className:"flex items-center space-x-2 text-blue-400 hover:text-blue-300 transition-colors duration-200 hover:scale-105 transform",children:[s.jsx(ro,{className:"w-8 h-8"}),s.jsx("span",{className:"text-lg",children:"LinkedIn"})]}),s.jsxs("button",{onClick:a,className:"flex items-center space-x-2 text-green-400 hover:text-green-300 transition-colors duration-200 hover:scale-105 transform",children:[s.jsx(Gt,{className:"w-8 h-8"}),s.jsx("span",{className:"text-lg",children:"Email"})]})]}),s.jsx("div",{className:"border-t border-gray-700 pt-6",children:s.jsxs("button",{onClick:i,className:"flex items-center space-x-2 text-yellow-400 hover:text-yellow-300 transition-colors duration-200 hover:scale-105 transform mx-auto",children:[s.jsx(Pa,{className:"w-8 h-8"}),s.jsx("span",{className:"text-lg",children:"Return to Desktop"})]})})]})]})},ql=()=>{const[e,t]=u.useState(null);return{isShuttingDown:!!e,shutdownType:e,triggerShutdown:a=>{t(a)},cancelShutdown:()=>{t(null)},resetToDesktop:()=>{t(null)}}},dn=[{id:"my_computer",name:"My Computer",icon:"💻",type:"system"},{id:"projects",name:"My Projects",icon:"📁",type:"folder"},{id:"my_resume",name:"My Resume.pdf",icon:"📄",type:"file"},{id:"skills",name:"Skills & Experience",icon:"✨",type:"folder"},{id:"contact",name:"Contact Info",icon:"📧",type:"shortcut"},{id:"about",name:"About Me",icon:"👤",type:"file"},{id:"recyclebin",name:"Recycle Bin",icon:"🗑️",type:"system"}],un=[{id:"my_computer",name:"My Computer",icon:"💻"},{id:"projects",name:"My Projects",icon:"📁"},{id:"my_resume",name:"My Resume.pdf",icon:"📄"},{id:"skills",name:"Skills & Experience",icon:"✨"},{id:"contact",name:"Contact Info",icon:"📧"},{id:"about",name:"About Me",icon:"👤"}],ur=768;function Ul(){const[e,t]=u.useState(void 0);return u.useEffect(()=>{const r=window.matchMedia(`(max-width: ${ur-1}px)`),o=()=>{t(window.innerWidth<ur)};return r.addEventListener("change",o),t(window.innerWidth<ur),()=>r.removeEventListener("change",o)},[]),!!e}const Yl=({onClose:e,onOpenWindow:t})=>{const r=(o,n)=>{t(o,n),e()};return s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-30 z-[9998] flex items-center justify-center animate-in fade-in",onClick:e,children:s.jsxs("div",{className:"bg-[#ECE9D8] border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl w-[450px] rounded-lg overflow-hidden animate-slide-up",onClick:o=>o.stopPropagation(),style:{fontFamily:"Tahoma, sans-serif"},children:[s.jsxs("div",{className:"bg-gradient-to-r from-[#2155C4] to-[#4D90FE] text-white font-bold text-sm px-3 py-1 flex justify-between items-center cursor-default",children:[s.jsx("span",{children:"Welcome!"}),s.jsx("button",{onClick:e,className:"bg-[#E2614A] hover:bg-[#F07A68] border border-[#8C3B2E] text-white w-5 h-5 flex items-center justify-center rounded-sm text-xs font-mono",children:s.jsx(yt,{size:14})})]}),s.jsxs("div",{className:"p-4 text-sm",children:[s.jsx("div",{className:"flex items-start space-x-3",children:s.jsxs("div",{children:[s.jsx("h2",{className:"font-bold text-lg mb-2",children:"Welcome to my Portfolio!"}),s.jsx("p",{className:"mb-4",children:"You can explore by double-clicking desktop icons or using the Start menu."}),s.jsx("p",{className:"font-semibold mb-2",children:"For quick access:"})]})}),s.jsxs("div",{className:"mt-2 space-y-2",children:[s.jsxs("button",{onClick:()=>r("my_resume","my_resume.pdf"),className:"w-full flex items-center space-x-3 px-3 py-2 bg-white border rounded hover:bg-gray-100 transition-colors text-left",children:[s.jsx(De,{className:"text-red-600",size:20}),s.jsx("span",{className:"font-semibold",children:"View Resume"})]}),s.jsxs("button",{onClick:()=>r("projects","My Projects"),className:"w-full flex items-center space-x-3 px-3 py-2 bg-white border rounded hover:bg-gray-100 transition-colors text-left",children:[s.jsx(ft,{className:"text-yellow-600",size:20}),s.jsx("span",{className:"font-semibold",children:"See My Projects"})]}),s.jsxs("button",{onClick:()=>r("contact","Contact Info"),className:"w-full flex items-center space-x-3 px-3 py-2 bg-white border rounded hover:bg-gray-100 transition-colors text-left",children:[s.jsx(Gt,{className:"text-red-600",size:20}),s.jsx("span",{className:"font-semibold",children:"Get In Touch"})]})]}),s.jsx("div",{className:"text-right mt-4",children:s.jsx("button",{onClick:e,className:"px-4 py-1 bg-gray-200 border border-gray-400 rounded-sm hover:bg-gray-300",children:"Close"})})]})]})})},ms=[{id:"bad_code",name:"bad_code.js",type:"JavaScript File",size:"2.3 KB",modified:"2019-03-15",content:`// Oh the humanity! My early JavaScript adventures...

// Exhibit A: Global variable soup
var name;
var age;
var email;
var address;
var phone;
var favoriteColor;
var petName;
// ... and 47 more global variables

// Exhibit B: The infamous pyramid of doom
function getUserData(userId) {
  $.ajax({
    url: '/api/user/' + userId,
    success: function(user) {
      $.ajax({
        url: '/api/user/' + userId + '/posts',
        success: function(posts) {
          $.ajax({
            url: '/api/user/' + userId + '/comments',
            success: function(comments) {
              // Finally! Only 3 callbacks deep...
              displayUserProfile(user, posts, comments);
            }
          });
        }
      });
    }
  });
}

// Exhibit C: The classic "works on my machine"
if (navigator.userAgent.indexOf("Chrome") > -1) {
  // Code that only works in Chrome on Windows 10
  // on a Tuesday, when Mercury is in retrograde
}`},{id:"first_website",name:"first_website.html",type:"HTML Document",size:"15.7 KB",modified:"2018-07-22",content:`<!DOCTYPE html>
<html>
<head>
  <title>WELCOME TO MY AWESOME WEBSITE!!!</title>
  <style>
    /* I just discovered CSS animations... */
    body {
      background: linear-gradient(45deg, #ff0000, #00ff00, #0000ff, #ffff00);
      animation: rainbow 2s infinite;
      font-family: Comic Sans MS; /* The pinnacle of web design */
    }

    @keyframes rainbow {
      0% { filter: hue-rotate(0deg); }
      100% { filter: hue-rotate(360deg); }
    }

    .blink {
      animation: blink 0.5s infinite;
    }

    @keyframes blink {
      0% { opacity: 1; }
      50% { opacity: 0; }
    }
  </style>
</head>
<body>
  <center> <!-- Because centering was hard back then -->
    <h1 class="blink">JOHN'S SUPER COOL WEBSITE </h1>
    <marquee>Welcome to the future of web design!</marquee>

    <table border="1" cellpadding="10" bgcolor="yellow">
      <tr>
        <td>
          <h2>About Me:</h2>
          <p>I like computers and stuff. I know HTML now so I'm basically a hacker.</p>

          <h2>My Skills:</h2>
          <ul>
            <li>HTML (I'm an expert now!)</li>
            <li>MS Paint</li>
            <li>Typing really fast</li>
          </ul>

          <h2>Guestbook:</h2>
          <p>Please sign my guestbook! (Coming soon...)</p>

          <h2>Hit Counter:</h2>
          <img src="counter.gif" alt="You are visitor #1">
          <!-- Spoiler: I was always visitor #1 -->
        </td>
      </tr>
    </table>

    <p><img src="under_construction.gif"> This site is under construction! <img src="under_construction.gif"></p>
  </center>
</body>
</html>

<!-- Current me looking at this: "How did anyone hire me?" -->`},{id:"spaghetti_code",name:"spaghetti_code.cs",type:"C# File",size:"9.2 KB",modified:"2020-02-15",content:`// The legendary spaghetti monster I once called "clean code"
// This C# version is just as... flavorful.

using System;
using System.Linq;

namespace EarlyCareerMistakes
{
  public class SpaghettiCode
  {
    // Exhibit A: The pyramid of doom, C# edition
    public string ProcessUserData(object[] data)
    {
      if (data != null) {
        if (data.Length > 0) {
          if (data[0] != null) {
            if (data[0] is string strData && !string.IsNullOrWhiteSpace(strData)) {
              var userName = strData.Trim().ToLower();
              if (userName != "" && userName.Length >= 2 && userName.All(char.IsLetter)) {
                // I could have done this in one line with a null conditional and a regex.
                // Instead, I chose... this.
                return "Valid user!";
              }
            }
          }
        }
      }
      return "Invalid";
    }

    // Exhibit B: The "God Method" that does everything
    public string HandleEverything()
    {
      ConnectToDatabase();
      AuthenticateUser();
      ProcessSomeData();
      SendEmail();
      WriteToLog();
      SaveToFile();
      CallExternalApi();
      // ... and it's probably missing a try-catch block.
      return "Success... I think?";
    }

    // Exhibit C: My naming conventions were... creative
    public object Foo()
    {
      var bar = GetStuff();
      var thing = DoSomething(bar);
      // Why did I think this ternary was necessary?
      var result = thing != null ? thing : null;
      return result;
    }

    // Exhibit D: Copy-paste driven development
    public decimal CalculateTotal1(dynamic[] items)
    {
      decimal total = 0;
      foreach (var item in items) {
        total += item.Price * item.Quantity;
        if (item.Discount > 0) {
          total -= item.Discount;
        }
      }
      return total;
    }

    public decimal CalculateTotal2(dynamic[] items)
    {
      decimal total = 0;
      foreach (var item in items) {
        total += item.Price * item.Quantity;
        if (item.Discount > 0) {
          total -= item.Discount;
        }
      }
      return total;
    }
    // (Yes, they're identical. I probably got distracted by a squirrel.)

    // Modern me: *refactored into 15 clean, testable classes with dependency injection*
  }

  // Dummy implementations to make it look like it does something
  public partial class SpaghettiCode {
    private void CallExternalApi() {}
    private void SaveToFile() {}
    private void WriteToLog() {}
    private void SendEmail() {}
    private void ProcessSomeData() {}
    private object AuthenticateUser() => null;
    private object ConnectToDatabase() => null;
    private object GetStuff() => null;
    private object DoSomething(object bar) => null;
  }
}`},{id:"debugging_tears",name:"debugging_tears.txt",type:"Text Document",size:"4.2 KB",modified:"2021-09-30",content:`THE GREAT DEBUGGING DISASTERS OF MY CAREER
==========================================

Bug #1: The Missing Semicolon Saga (3 days)
Date: March 2019
Problem: Page wouldn't load, 500 server error
Root cause: Missing semicolon in JavaScript
Debugging methods tried:
- Rewrote entire backend (twice)
- Questioned life choices
- Considered career change to farming
- Asked 17 different Stack Overflow questions
- Rubber duck debugging (the duck quit)
Resolution: Senior dev spotted it in 30 seconds

Bug #2: The Case-Sensitive Catastrophe (1 week)
Date: July 2020
Problem: Database queries failing in production but working locally
Debugging journey:
- Blamed the cloud provider
- Rewrote all SQL queries
- Created 47 test cases
- Migrated to different database engine
- Sacrificed coffee to the code gods
Root cause: Linux server was case-sensitive, Windows dev machine wasn't
MyTable ≠ mytable (who knew?) 

Bug #3: The Phantom Form Data (2 weeks)
Date: November 2020
Problem: Form submissions randomly disappearing
Suspects investigated:
- Network gremlins
- Database corruption
- Competitor sabotage
- Quantum interference
- User error (always blame the user first)
Real culprit: Browser autocomplete interfering with custom validation
The user WAS clicking submit... it just wasn't being submitted 

Bug #4: The Docker Dilemma (4 days)
Date: April 2021
Problem: "Works on my machine" but not in container
Emotional stages:
1. Denial: "Docker is broken"
2. Anger: "WHO WROTE THIS DOCKERFILE?!" (spoiler: it was me)
3. Bargaining: "Maybe if I restart everything 50 times..."
4. Depression: "I'll never understand containers"
5. Acceptance: "Oh, I forgot to expose port 3000" 

Lessons Learned:
- Sleep is not optional during debugging
- Coffee is a debugging tool, not a food group
- The bug is always in the last place you look (because you stop looking)
- "It can't be that simple" - it usually is
- Your rubber duck is your best friend
- Stack Overflow doesn't judge... much
- When in doubt, restart everything (including yourself)

Current debugging motto: "Every bug is just an opportunity to feel really 
stupid for a few hours before feeling really smart." `},{id:"old_resume",name:"old_resume_v1.doc",type:"Word Document",size:"127 KB",modified:"2018-05-15",content:`JOHN SMITH - COMPUTER EXPERT
============================

OBJECTIVE:
To get a job with computers and make websites that are really cool.

SKILLS:
• Microsoft Word (Expert level - I can change fonts!)
• Internet Explorer (I know all the good websites)
• Copy and paste (I'm really fast at this)
• Typing (50 WPM on a good day)
• HTML (I made a webpage once)
• Computer troubleshooting (I know how to restart)

EXPERIENCE:
• Fixed my neighbor's computer by turning it off and on again (2018)
• Created a MySpace profile with custom music (2007)
• Won solitaire 3 times in a row (2017)
• Successfully installed a screensaver (2018)

EDUCATION:
• High School Diploma
• YouTube University (ongoing)
• Google Search Academy (self-taught)
• Stack Overflow Fellowship (unofficial)

PROJECTS:
• Built a website with flashing text and music that plays automatically
• Customized my Windows XP desktop with cool themes
• Learned to use Ctrl+C and Ctrl+V

REFERENCES:
• My mom (she says I'm really good with computers)
• My friend Steve (he taught me about right-clicking)

---

Current me looking at this: "Ah, the confidence of knowing just enough 
to be dangerous... and putting EVERYTHING in Comic Sans font." 

P.S. - Yes, I actually thought "Computer Expert" was a job title.
P.P.S. - The screensaver installation really was a proud moment.`},{id:"imposter_syndrome",name:"imposter_syndrome.exe",type:"Application",size:"∞ KB",modified:"Every day",content:` IMPOSTER SYNDROME SIMULATOR 2024 
=====================================

[LOADING SELF-DOUBT...]
[████████████████████████████████] 100%

Daily Thoughts Include:
• "Everyone here is smarter than me"
• "I'm just googling everything"
• "They'll find out I don't know what I'm doing"
• "I probably got this job by accident"
• "Why did they ask ME to lead this project?"

Reality Check:
- You've successfully delivered 15+ projects
- Your code reviews are thorough and helpful
- Junior devs come to YOU for guidance
- You solved that critical production bug last month
- You've learned 8 new technologies this year

Plot Twist:
The fact that you worry about not knowing enough 
actually means you care about doing good work!

Remember:
• Senior developers Google things too
• Everyone started somewhere
• Your "obvious" solution helped 3 people today
• You're not supposed to know everything
• Growth means being uncomfortable sometimes

Achievement Unlocked:
"Turned imposter syndrome into motivation to keep learning"

[CONFIDENCE BOOST DELIVERED]
[CONTINUE BEING AWESOME? Y/N] → Y

---
"The only thing I know is that I know nothing... 
and that's actually pretty valuable in tech!" - Socrates (probably)`},{id:"stack_overflow_dependency",name:"stack_overflow_dependency.dll",type:"Dynamic Link Library",size:"42.0 GB",modified:"Constantly",content:`STACK OVERFLOW DEPENDENCY MANAGER v2024.1
=========================================

USAGE STATISTICS:
• Questions asked: 47
• Questions answered: 3 (and one was marked as duplicate)
• Times saved by existing answers: 2,847
• Hours spent reading similar problems: 847
• "Thank you" comments left: 156

REPUTATION BREAKDOWN:
• Upvotes received: 23
• Downvotes received: 7 (apparently "just use jQuery" isn't always the answer)
• Best answer: +15 points for explaining CSS flexbox
• Most embarrassing question: -3 points for asking about HTML closing tags

MOST SEARCHED PHRASES:
1. "How to center a div"
2. "JavaScript array methods"
3. "Git undo last commit"
4. "CSS not working"
5. "Why is my React component not rendering"
6. "Difference between let and var"
7. "How to exit vim" (we've all been there)

CLASSIC RESPONSES ENCOUNTERED:
• "Possible duplicate of..." (usually not)
• "What have you tried?" (everything!)
• "This question is too broad"
• "Welcome to Stack Overflow! Please read the FAQ"
• "Try this: [one line of code that solves everything]"

KARMA PHILOSOPHY:
• Always upvote helpful answers
• Leave comments explaining what worked
• Pay it forward by answering when you can
• Remember: everyone was a beginner once

DEPENDENCY WARNING:
This library is critical to developer productivity.
Removal may result in:
- Severe coding paralysis
- Increased rubber duck conversations
- Excessive reading of documentation
- Actually understanding how things work (scary!)

ACKNOWLEDGMENTS:
Thank you to the anonymous heroes who answered 
"How to do [basic thing]" with patience and detailed examples.
You're the real MVPs of software development.

[STATUS: HEALTHY DEPENDENCY]
[UPDATES: AVAILABLE DAILY]`},{id:"coffee_addiction",name:"coffee_addiction.jar",type:"Java Archive",size:"3.14 MB",modified:"Every morning",content:`COFFEE DEPENDENCY INJECTION FRAMEWORK
==========================================

CONSUMPTION METRICS:
• Daily intake: 4.7 cups average
• Peak performance hours: 9-11 AM (post-coffee #2)
• Crash time: 3 PM (solution: more coffee)
• Weekend consumption: Doubled (no meetings to dilute coding time)

BREW CONFIGURATIONS:
• Development Environment: Light roast, extra caffeine
• Production Deployment: Dark roast, triple shot
• Bug Fixing Mode: Whatever's strongest
• Code Review Sessions: Decaf (need to be diplomatic)

CODE CORRELATION:
Lines of code vs. Coffee cups consumed:
0 cups  → Stares at screen, questions life choices
1 cup   → Opens IDE, reads yesterday's code
2 cups  → Actual coding begins
3 cups  → Peak productivity achieved
4+ cups → Either genius-level code or complete gibberish

WITHDRAWAL SYMPTOMS:
• Syntax errors increase by 347%
• Variable naming becomes questionable (x, xx, xxx)
• Comments consist of only "//TODO: fix this mess"
• Meetings become physically painful
• Considers PowerShell as a viable career alternative

PERFORMANCE OPTIMIZATION:
• Cold brew for sustained release
• Espresso for immediate compilation
• French press for long debugging sessions
• Energy drinks for production hotfixes (emergency use only)

ACHIEVEMENTS UNLOCKED:
- "Coffee Connoisseur" - Tried 47 different roasts
- "Morning Ritual Master" - Perfect brew timing
- "Team Supplier" - Always brings extra for colleagues
- "Crisis Manager" - Functions at 3 AM with proper fuel

HEALTH WARNINGS:
• Side effects may include: productivity, alertness, happiness
• Do not operate heavy machinery (deploy to production) without adequate doses
• Consult your IDE before changing to decaf

FUTURE ROADMAP:
• Integrate IoT coffee maker with CI/CD pipeline
• Automatic brewing triggered by failed builds
• Slack bot for coffee status updates
• Machine learning to optimize brew strength vs. code quality

[STATUS: ACTIVE DEPENDENCY]
[NEXT REFILL: In 47 minutes]`},{id:"work_life_balance",name:"work_life_balance.404",type:"File Not Found",size:"0 KB",modified:"Never",content:`ERROR 404: WORK-LIFE BALANCE NOT FOUND
======================================

SEARCH RESULTS:
We couldn't find "work_life_balance" in the following locations:
• /home/<USER>/life/
• /usr/local/priorities/
• /var/log/activities/
• /etc/schedule/
• /tmp/weekend_plans/ (directory empty)

LAST SEEN:
• Before learning to code
• During lunch breaks (deprecated)
• Weekends (status: hijacked by side projects)
• Vacation days (used for learning new frameworks)

CURRENT STATUS:
• Work hours: 9 AM - 5 PM (and 8 PM - 11 PM) (and weekends)
• Side projects: 47 active repositories
• Tutorial queue: 23 videos, 156 articles
• Sleep schedule: "I'll fix it tomorrow"
• Social life: Exists primarily on Discord servers

SYMPTOMS OF MISSING FILE:
• Dreams in JavaScript
• Explains APIs to confused family members
• Weekend "relaxation" involves coding tutorials
• Phone wallpaper is a terminal screenshot
• Considers Stack Overflow a social network

RECOVERY ATTEMPTS:
- [X] Set boundaries (lasted 3 days)
- [X] No-code weekends (built 2 apps)
- [X] Meditation app (analyzed its UI/UX instead)
- [X] Take up hiking (brought laptop to research trail apps)
- [X] Join a book club (suggested technical books only)

TROUBLESHOOTING STEPS:
1. Try turning off notifications (but what if there's an important GitHub issue?)
2. Schedule non-tech activities (ended up automating the scheduling)
3. Find hobbies unrelated to coding (built an app to track hobbies)
4. Spend time with non-tech friends (converted them to junior developers)

PLOT TWIST:
Maybe work-life balance is about loving what you do 
so much that the boundaries naturally blur?

CURRENT APPROACH:
• Work on passion projects (technically still work?)
• Code with friends (technically social time?)
• Automate life tasks (more time for coding!)
• Teaching others (spreading the joy)

WARNING:
This file may never be fully restored. 
Symptoms include excessive happiness, 
constant learning, and the urge to optimize everything.

[RESTORATION: IN PROGRESS...]
[ESTIMATED TIME: When robots do all the coding]
[PRIORITY: Lower than the current sprint]

P.S. - At least I'm consistent!`}],Kl={js:$e,html:$e,py:$e,ts:$e,tsx:$e,css:$e,json:Ca,txt:De,doc:De,docx:De,pdf:De,exe:Sa,dll:nr,jar:nr,zip:nr,404:Na},Lr=({filename:e,...t})=>{var n;const r=((n=e.split(".").pop())==null?void 0:n.toLowerCase())||"",o=Kl[r]||Ta;return s.jsx(o,{...t})},Ql=({files:e,selectedFile:t,onFileSelect:r})=>s.jsx("div",{className:"w-64 bg-white border-r border-gray-300 flex flex-col",children:s.jsx("div",{className:"flex-1 overflow-y-auto",children:e.map(o=>s.jsxs("div",{className:`p-2 border-b border-gray-200 cursor-pointer hover:bg-blue-50 ${t===o.id?"bg-blue-100 border-l-4 border-l-blue-500":""}`,onClick:()=>r(o.id),children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[s.jsx(Lr,{filename:o.name,className:"h-5 w-5 text-gray-700 flex-shrink-0"}),s.jsx("span",{className:"text-sm font-medium truncate",children:o.name})]}),s.jsxs("div",{className:"text-xs text-gray-500 pl-7",children:[s.jsx("div",{children:o.type}),s.jsxs("div",{children:[o.size," • ",o.modified]})]})]},o.id))})}),Xl=({file:e})=>s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"bg-white border-b border-gray-300 p-3",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Lr,{filename:e.name,className:"h-6 w-6 text-gray-800"}),s.jsxs("div",{children:[s.jsx("h2",{className:"font-bold text-gray-800",children:e.name}),s.jsxs("p",{className:"text-sm text-gray-600",children:[e.type," • ",e.size," • Modified ",e.modified]})]})]})}),s.jsx("div",{className:"flex-1 overflow-auto p-4",children:s.jsx("div",{className:"bg-white rounded border border-gray-300 p-4 font-mono text-sm whitespace-pre-line",children:e.content})})]}),Jl=()=>s.jsx("div",{className:"flex-1 flex items-center justify-center",children:s.jsxs("div",{className:"text-center text-gray-500",children:[s.jsx(Lr,{filename:"recycle.bin",className:"h-16 w-16 mx-auto mb-4 text-gray-400"}),s.jsx("h2",{className:"text-lg font-bold mb-2",children:"Welcome to the Developer's Recycle Bin"}),s.jsx("p",{className:"text-sm max-w-md",children:`A collection of my coding journey's "learning opportunities" - from embarrassing early code to debugging disasters. Click on any file to see my evolution as a developer!`}),s.jsx("div",{className:"mt-4 text-xs text-gray-400",children:`"One developer's trash is... well, still trash, but it's educational trash!"`})]})}),Zl=()=>{const[e,t]=u.useState(null),r=ms.find(o=>o.id===e);return s.jsxs("div",{className:"h-full flex bg-gray-50 font-tahoma",children:[s.jsx(Ql,{files:ms,selectedFile:e,onFileSelect:t}),s.jsx("div",{className:"flex-1 flex flex-col",children:e&&r?s.jsx(Xl,{file:r}):s.jsx(Jl,{})})]})};var hn={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},fs=C.createContext&&C.createContext(hn),ec=["attr","size","title"];function tc(e,t){if(e==null)return{};var r=rc(e,t),o,n;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)o=a[n],!(t.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(e,o)&&(r[o]=e[o])}return r}function rc(e,t){if(e==null)return{};var r={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}function Ft(){return Ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},Ft.apply(this,arguments)}function ps(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function Lt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ps(Object(r),!0).forEach(function(o){sc(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ps(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function sc(e,t,r){return t=oc(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oc(e){var t=nc(e,"string");return typeof t=="symbol"?t:t+""}function nc(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(typeof o!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function mn(e){return e&&e.map((t,r)=>C.createElement(t.tag,Lt({key:r},t.attr),mn(t.child)))}function F(e){return t=>C.createElement(ac,Ft({attr:Lt({},e.attr)},t),mn(e.child))}function ac(e){var t=r=>{var{attr:o,size:n,title:a}=e,i=tc(e,ec),l=n||r.size||"1em",d;return r.className&&(d=r.className),e.className&&(d=(d?d+" ":"")+e.className),C.createElement("svg",Ft({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,o,i,{className:d,style:Lt(Lt({color:e.color||r.color},r.style),e.style),height:l,width:l,xmlns:"http://www.w3.org/2000/svg"}),a&&C.createElement("title",null,a),e.children)};return fs!==void 0?C.createElement(fs.Consumer,null,r=>t(r)):t(hn)}function ic(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M16.712 17.711H7.288l-1.204 2.916L12 24l5.916-3.373-1.204-2.916ZM14.692 0l7.832 16.855.814-12.856L14.692 0ZM9.308 0 .662 3.999l.814 12.856L9.308 0Zm-.405 13.93h6.198L12 6.396 8.903 13.93Z"},child:[]}]})(e)}function lc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M18.78 12.653c-2.882 0-5.22 2.336-5.22 5.22s2.338 5.22 5.22 5.22 5.22-2.34 5.22-5.22-2.336-5.22-5.22-5.22zm-13.56 0c-2.88 0-5.22 2.337-5.22 5.22s2.338 5.22 5.22 5.22 5.22-2.338 5.22-5.22-2.336-5.22-5.22-5.22zm12-6.525c0 2.883-2.337 5.22-5.22 5.22-2.882 0-5.22-2.337-5.22-5.22 0-2.88 2.338-5.22 5.22-5.22 2.883 0 5.22 2.34 5.22 5.22z"},child:[]}]})(e)}function cc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M.778 1.213a.768.768 0 00-.768.892l3.263 19.81c.084.5.515.868 1.022.873H19.95a.772.772 0 00.77-.646l3.27-20.03a.768.768 0 00-.768-.891zM14.52 15.53H9.522L8.17 8.466h7.561z"},child:[]}]})(e)}function dc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M.87 18.257c-.248.382-.53.875-.763 1.245a.764.764 0 0 0 .255 1.04l4.965 3.054a.764.764 0 0 0 1.058-.26c.199-.332.454-.763.733-1.221 1.967-3.247 3.945-2.853 7.508-1.146l4.957 2.337a.764.764 0 0 0 1.028-.382l2.364-5.346a.764.764 0 0 0-.382-1 599.851 599.851 0 0 1-4.965-2.361C10.911 10.97 5.224 11.185.87 18.257zM23.131 5.743c.249-.405.531-.875.764-1.25a.764.764 0 0 0-.256-1.034L18.675.404a.764.764 0 0 0-1.058.26c-.195.335-.451.763-.734 1.225-1.966 3.246-3.945 2.85-7.508 1.146L4.437.694a.764.764 0 0 0-1.027.382L1.046 6.422a.764.764 0 0 0 .382 1c1.039.49 3.105 1.467 4.965 2.361 6.698 3.246 12.392 3.029 16.738-4.04z"},child:[]}]})(e)}function uc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M1.5 0h21l-1.91 21.563L11.977 24l-8.565-2.438L1.5 0zm17.09 4.413L5.41 4.41l.213 2.622 10.125.002-.255 2.716h-6.64l.24 2.573h6.182l-.366 3.523-2.91.804-2.956-.81-.188-2.11h-2.61l.29 3.855L12 19.288l5.373-1.53L18.59 4.414z"},child:[]}]})(e)}function hc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M1.4 0h21.2c.8 0 1.4.6 1.4 1.4v1.1c0 .8-.6 1-.9 1C11.7 4.9 4.2 9.7 0 14.1V1.4C0 .6.6 0 1.4 0zm.022 19.567L1.7 19.2C5.3 14.6 12.4 8.3 24 6.3v16.3c0 .8-.6 1.4-1.4 1.4H1.4C.6 24 0 23.4 0 22.6v-.4c0-.3.2-.8.3-.9.252-.589.646-1.107 1.122-1.733z"},child:[]}]})(e)}function mc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M13.983 11.078h2.119a.186.186 0 00.186-.185V9.006a.186.186 0 00-.186-.186h-2.119a.185.185 0 00-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 00.186-.186V3.574a.186.186 0 00-.186-.185h-2.118a.185.185 0 00-.185.185v1.888c0 .102.082.185.185.185m0 2.716h2.118a.187.187 0 00.186-.186V6.29a.186.186 0 00-.186-.185h-2.118a.185.185 0 00-.185.185v1.887c0 .102.082.185.185.186m-2.93 0h2.12a.186.186 0 00.184-.186V6.29a.185.185 0 00-.185-.185H8.1a.185.185 0 00-.185.185v1.887c0 .102.083.185.185.186m-2.964 0h2.119a.186.186 0 00.185-.186V6.29a.185.185 0 00-.185-.185H5.136a.186.186 0 00-.186.185v1.887c0 .102.084.185.186.186m5.893 2.715h2.118a.186.186 0 00.186-.185V9.006a.186.186 0 00-.186-.186h-2.118a.185.185 0 00-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 00.184-.185V9.006a.185.185 0 00-.184-.186h-2.12a.185.185 0 00-.184.185v1.888c0 .102.083.185.185.185m-2.964 0h2.119a.185.185 0 00.185-.185V9.006a.185.185 0 00-.184-.186h-2.12a.186.186 0 00-.186.186v1.887c0 .102.084.185.186.185m-2.92 0h2.12a.185.185 0 00.184-.185V9.006a.185.185 0 00-.184-.186h-2.12a.185.185 0 00-.184.185v1.888c0 .102.082.185.185.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338.001-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 00-.75.748 11.376 11.376 0 00.692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983.003 1.963-.086 2.93-.266a12.248 12.248 0 003.823-1.389c.98-.567 1.86-1.288 2.61-2.136 1.252-1.418 1.998-2.997 2.553-4.4h.221c1.372 0 2.215-.549 2.68-1.009.309-.293.55-.65.707-1.046l.098-.288Z"},child:[]}]})(e)}function ht(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M24 8.77h-2.468v7.565h-1.425V8.77h-2.462V7.53H24zm-6.852 7.565h-4.821V7.53h4.63v1.24h-3.205v2.494h2.953v1.234h-2.953v2.604h3.396zm-6.708 0H8.882L4.78 9.863a2.896 2.896 0 0 1-.258-.51h-.036c.032.189.048.592.048 1.21v5.772H3.157V7.53h1.659l3.965 6.32c.167.261.275.442.323.54h.024c-.04-.233-.06-.629-.06-1.185V7.529h1.372zm-8.703-.693a.868.829 0 0 1-.869.829.868.829 0 0 1-.868-.83.868.829 0 0 1 .868-.828.868.829 0 0 1 .869.829Z"},child:[]}]})(e)}function fc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M13.394 0C8.683 0 4.609 2.716 2.644 6.667h15.641a4.77 4.77 0 0 0 3.073-1.11c.446-.375.864-.785 1.247-1.243l.001-.002A11.974 11.974 0 0 0 13.394 0zM1.804 8.889a12.009 12.009 0 0 0 0 6.222h14.7a3.111 3.111 0 1 0 0-6.222zm.84 8.444C4.61 21.283 8.684 24 13.395 24c3.701 0 7.011-1.677 9.212-4.312l-.001-.002a9.958 9.958 0 0 0-1.247-1.243 4.77 4.77 0 0 0-3.073-1.11z"},child:[]}]})(e)}function pc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M23.546 10.93L13.067.452c-.604-.603-1.582-.603-2.188 0L8.708 2.627l2.76 2.76c.645-.215 1.379-.07 1.889.441.516.515.658 1.258.438 1.9l2.658 2.66c.645-.223 1.387-.078 1.9.435.721.72.721 1.884 0 2.604-.719.719-1.881.719-2.6 0-.539-.541-.674-1.337-.404-1.996L12.86 8.955v6.525c.176.086.342.203.488.348.713.721.713 1.883 0 2.6-.719.721-1.889.721-2.609 0-.719-.719-.719-1.879 0-2.598.182-.18.387-.316.605-.406V8.835c-.217-.091-.424-.222-.6-.401-.545-.545-.676-1.342-.396-2.009L7.636 3.7.45 10.881c-.6.605-.6 1.584 0 2.189l10.48 10.477c.604.604 1.582.604 2.186 0l10.43-10.43c.605-.603.605-1.582 0-2.187"},child:[]}]})(e)}function gc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"m23.6004 9.5927-.0337-.0862L20.3.9814a.851.851 0 0 0-.3362-.405.8748.8748 0 0 0-.9997.0539.8748.8748 0 0 0-.29.4399l-2.2055 6.748H7.5375l-2.2057-6.748a.8573.8573 0 0 0-.29-.4412.8748.8748 0 0 0-.9997-.0537.8585.8585 0 0 0-.3362.4049L.4332 9.5015l-.0325.0862a6.0657 6.0657 0 0 0 2.0119 7.0105l.0113.0087.03.0213 4.976 3.7264 2.462 1.8633 1.4995 1.1321a1.0085 1.0085 0 0 0 1.2197 0l1.4995-1.1321 2.4619-1.8633 5.006-3.7489.0125-.01a6.0682 6.0682 0 0 0 2.0094-7.003z"},child:[]}]})(e)}function xc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M1.5 0h21l-1.91 21.563L11.977 24l-8.564-2.438L1.5 0zm7.031 9.75l-.232-2.718 10.059.003.23-2.622L5.412 4.41l.698 8.01h9.126l-.326 3.426-2.91.804-2.955-.81-.188-2.11H6.248l.33 4.171L12 19.351l5.379-1.443.744-8.157H8.531z"},child:[]}]})(e)}function bc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M0 0h24v24H0V0zm22.034 18.276c-.175-1.095-.888-2.015-3.003-2.873-.736-.345-1.554-.585-1.797-1.14-.091-.33-.105-.51-.046-.705.15-.646.915-.84 1.515-.66.39.12.75.42.976.9 1.034-.676 1.034-.676 1.755-1.125-.27-.42-.404-.601-.586-.78-.63-.705-1.469-1.065-2.834-1.034l-.705.089c-.676.165-1.32.525-1.71 1.005-1.14 1.291-.811 3.541.569 4.471 1.365 1.02 3.361 1.244 3.616 2.205.24 1.17-.87 1.545-1.966 1.41-.811-.18-1.26-.586-1.755-1.336l-1.83 1.051c.21.48.45.689.81 1.109 1.74 1.756 6.09 1.666 6.871-1.004.029-.09.24-.705.074-1.65l.046.067zm-8.983-7.245h-2.248c0 1.938-.009 3.864-.009 5.805 0 1.232.063 2.363-.138 2.711-.33.689-1.18.601-1.566.48-.396-.196-.597-.466-.83-.855-.063-.105-.11-.196-.127-.196l-1.825 1.125c.305.63.75 1.172 1.324 1.517.855.51 2.004.675 3.207.405.783-.226 1.458-.691 1.811-1.411.51-.93.402-2.07.397-3.346.012-2.054 0-4.109 0-6.179l.004-.056z"},child:[]}]})(e)}function vc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M11.571 11.513H0a5.218 5.218 0 0 0 5.232 5.215h2.13v2.057A5.215 5.215 0 0 0 12.575 24V12.518a1.005 1.005 0 0 0-1.005-1.005zm5.723-5.756H5.736a5.215 5.215 0 0 0 5.215 5.214h2.129v2.058a5.218 5.218 0 0 0 5.215 5.214V6.758a1.001 1.001 0 0 0-1.001-1.001zM23.013 0H11.455a5.215 5.215 0 0 0 5.215 5.215h2.129v2.057A5.215 5.215 0 0 0 24 12.483V1.005A1.001 1.001 0 0 0 23.013 0Z"},child:[]}]})(e)}function yc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M12.043 23.968c.479-.004.953-.029 1.426-.094a11.805 11.805 0 003.146-.863 12.404 12.404 0 003.793-2.542 11.977 11.977 0 002.44-3.427 11.794 11.794 0 001.02-3.476c.149-1.16.135-2.346-.045-3.499a11.96 11.96 0 00-.793-2.788 11.197 11.197 0 00-.854-1.617c-1.168-1.837-2.861-3.314-4.81-4.3a12.835 12.835 0 00-2.172-.87h-.005c.119.063.24.132.345.201.12.074.239.146.351.225a8.93 8.93 0 011.559 1.33c1.063 1.145 1.797 2.548 2.218 4.041.284.982.434 1.998.495 3.017.044.743.044 1.491-.047 2.229-.149 1.27-.554 2.51-1.228 3.596a7.475 7.475 0 01-1.903 2.084c-1.244.928-2.877 1.482-4.436 1.114a3.916 3.916 0 01-.748-.258 4.692 4.692 0 01-.779-.45 6.08 6.08 0 01-1.244-1.105 6.507 6.507 0 01-1.049-1.747 7.366 7.366 0 01-.494-2.54c-.03-1.273.225-2.553.854-3.67a6.43 6.43 0 011.663-1.918c.225-.178.464-.333.704-.479l.016-.007a5.121 5.121 0 00-1.441-.12 4.963 4.963 0 00-1.228.24c-.359.12-.704.27-1.019.45a6.146 6.146 0 00-.733.494c-.211.18-.42.36-.615.555-1.123 1.153-1.768 2.682-2.022 4.256-.15.973-.15 1.96-.091 2.95.105 1.395.391 2.787.945 4.062a8.518 8.518 0 001.348 2.173 8.14 8.14 0 003.132 2.23 7.934 7.934 0 002.113.54c.074.015.149.015.209.015zm-2.934-.398a4.102 4.102 0 01-.45-.228 8.5 8.5 0 01-2.038-1.534c-1.094-1.137-1.827-2.566-2.247-4.08a15.184 15.184 0 01-.495-3.172 12.14 12.14 0 01.046-2.082c.135-1.257.495-2.501 1.124-3.58a6.889 6.889 0 011.783-2.053 6.23 6.23 0 011.633-.9 5.363 5.363 0 013.522-.045c.029 0 .029 0 .045.03.015.015.045.015.06.03.045.016.104.045.165.074.239.12.479.271.704.42a6.294 6.294 0 012.097 2.502c.42.914.615 1.934.631 2.938.014 1.079-.18 2.157-.645 3.146a6.42 6.42 0 01-2.638 2.832c.09.03.18.045.271.075.225.044.449.074.688.074 1.468.045 2.892-.66 3.94-1.647.195-.18.375-.375.54-.585.225-.27.435-.54.614-.823.239-.375.435-.75.614-1.154a8.112 8.112 0 00.509-1.664c.196-1.004.211-2.022.149-3.026-.135-2.022-.673-4.045-1.842-5.724a9.054 9.054 0 00-.555-.719 9.868 9.868 0 00-1.063-1.034 8.477 8.477 0 00-1.363-.915 9.927 9.927 0 00-1.692-.598l-.3-.06c-.209-.03-.42-.044-.634-.06a8.453 8.453 0 00-1.015.016c-.704.045-1.412.16-2.112.337C5.799 1.227 2.863 3.566 1.3 6.67A11.834 11.834 0 00.238 9.801a11.81 11.81 0 00-.104 3.775c.12 1.02.374 2.023.778 2.977.227.57.511 1.124.825 1.648 1.094 1.783 2.683 3.236 4.51 4.24.688.39 1.408.69 2.157.944.226.074.45.15.689.21z"},child:[]}]})(e)}function wc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M5.385 6.136c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.654 1.438 1.461zm-1.438 2.63c-.794 0-1.438.654-1.438 1.461s.644 1.461 1.438 1.461 1.438-.654 1.438-1.461-.644-1.461-1.438-1.461zm5.465-2.63c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.654 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.499-1.088 1.111.488 1.111 1.088 1.111 1.088-.498 1.088-1.111zm-1.088 5.592c.794 0 1.438-.654 1.438-1.461s-.644-1.461-1.438-1.461-1.438.654-1.438 1.461.643 1.461 1.438 1.461zm5.464-5.592c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.654 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.498-1.088 1.111S11.4 7.247 12 7.247s1.088-.498 1.088-1.111zm.35-4.675c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461S11.206 0 12 0s1.438.654 1.438 1.461zm-.35 0C13.088.848 12.6.35 12 .35s-1.088.498-1.088 1.111S11.4 2.572 12 2.572s1.088-.498 1.088-1.111zm.35 8.806c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.654 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.498-1.088 1.111.488 1.111 1.088 1.111 1.088-.499 1.088-1.111zm4.376-4.131c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.654 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.498-1.088 1.111.488 1.111 1.088 1.111 1.088-.498 1.088-1.111zm2.939 1.461c.794 0 1.438-.654 1.438-1.461s-.644-1.461-1.438-1.461-1.438.654-1.438 1.461.644 1.461 1.438 1.461zm-4.027 1.209c-.794 0-1.438.654-1.438 1.461s.644 1.461 1.438 1.461 1.438-.654 1.438-1.461-.643-1.461-1.438-1.461zm4.027 0c-.794 0-1.438.654-1.438 1.461s.644 1.461 1.438 1.461 1.438-.654 1.438-1.461-.644-1.461-1.438-1.461zM3.947 12.857a1.45 1.45 0 0 0-1.438 1.461c0 .807.644 1.461 1.438 1.461s1.438-.654 1.438-1.461a1.45 1.45 0 0 0-1.438-1.461zm5.465 1.5c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.655 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.498-1.088 1.111.488 1.111 1.088 1.111 1.088-.498 1.088-1.111zM12 12.896c-.794 0-1.438.654-1.438 1.461s.644 1.461 1.438 1.461 1.438-.654 1.438-1.461-.644-1.461-1.438-1.461zm5.464 1.461c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.655 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.498-1.088 1.111.488 1.111 1.088 1.111 1.088-.498 1.088-1.111zm2.939-1.461c-.794 0-1.438.654-1.438 1.461s.644 1.461 1.438 1.461 1.438-.654 1.438-1.461-.644-1.461-1.438-1.461zM3.947 16.948c-.794 0-1.438.654-1.438 1.461s.644 1.461 1.438 1.461 1.438-.654 1.438-1.461-.644-1.461-1.438-1.461zm5.465 1.5c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.654 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.498-1.088 1.111.488 1.111 1.088 1.111 1.088-.498 1.088-1.111zm4.376 0c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.654 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.498-1.088 1.111.488 1.111 1.088 1.111 1.088-.498 1.088-1.111zm.35 4.091c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.654 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.498-1.088 1.111S11.4 23.65 12 23.65s1.088-.498 1.088-1.111zm4.376-4.091c0 .807-.644 1.461-1.438 1.461s-1.438-.654-1.438-1.461.644-1.461 1.438-1.461 1.438.654 1.438 1.461zm-.35 0c0-.613-.488-1.111-1.088-1.111s-1.088.498-1.088 1.111.488 1.111 1.088 1.111 1.088-.498 1.088-1.111zm2.939-1.461c-.794 0-1.438.654-1.438 1.461s.644 1.461 1.438 1.461 1.438-.654 1.438-1.461-.644-1.461-1.438-1.461z"},child:[]}]})(e)}function jc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M16.405 5.501c-.115 0-.193.014-.274.033v.013h.014c.054.104.146.18.214.273.054.107.1.214.154.32l.014-.015c.094-.066.14-.172.14-.333-.04-.047-.046-.094-.08-.14-.04-.067-.126-.1-.18-.153zM5.77 18.695h-.927a50.854 50.854 0 00-.27-4.41h-.008l-1.41 4.41H2.45l-1.4-4.41h-.01a72.892 72.892 0 00-.195 4.41H0c.055-1.966.192-3.81.41-5.53h1.15l1.335 4.064h.008l1.347-4.064h1.095c.242 2.015.384 3.86.428 5.53zm4.017-4.08c-.378 2.045-.876 3.533-1.492 4.46-.482.716-1.01 1.073-1.583 1.073-.153 0-.34-.046-.566-.138v-.494c.11.017.24.026.386.026.268 0 .483-.075.647-.222.197-.18.295-.382.295-.605 0-.155-.077-.47-.23-.944L6.23 14.615h.91l.727 2.36c.164.536.233.91.205 1.123.4-1.064.678-2.227.835-3.483zm12.325 4.08h-2.63v-5.53h.885v4.85h1.745zm-3.32.135l-1.016-.5c.09-.076.177-.158.255-.25.433-.506.648-1.258.648-2.253 0-1.83-.718-2.746-2.155-2.746-.704 0-1.254.232-1.65.697-.43.508-.646 1.256-.646 2.245 0 .972.19 1.686.574 2.14.35.41.877.615 1.583.615.264 0 .506-.033.725-.098l1.325.772.36-.622zM15.5 17.588c-.225-.36-.337-.94-.337-1.736 0-1.393.424-2.09 1.27-2.09.443 0 .77.167.977.5.224.362.336.936.336 1.723 0 1.404-.424 2.108-1.27 2.108-.445 0-.77-.167-.978-.5zm-1.658-.425c0 .47-.172.856-.516 1.156-.344.3-.803.45-1.384.45-.543 0-1.064-.172-1.573-.515l.237-.476c.438.22.833.328 1.19.328.332 0 .593-.073.783-.22a.754.754 0 00.3-.615c0-.33-.23-.61-.648-.845-.388-.213-1.163-.657-1.163-.657-.422-.307-.632-.636-.632-1.177 0-.45.157-.81.47-1.085.315-.278.72-.415 1.22-.415.512 0 .98.136 1.4.41l-.213.476a2.726 2.726 0 00-1.064-.23c-.283 0-.502.068-.654.206a.685.685 0 00-.248.524c0 .328.234.61.666.85.393.215 1.187.67 1.187.67.433.305.648.63.648 1.168zm9.382-5.852c-.535-.014-.95.04-1.297.188-.1.04-.26.04-.274.167.055.053.063.14.11.214.08.134.218.313.346.407.14.11.28.216.427.31.26.16.555.255.81.416.145.094.293.213.44.313.073.05.12.14.214.172v-.02c-.046-.06-.06-.147-.105-.214-.067-.067-.134-.127-.2-.193a3.223 3.223 0 00-.695-.675c-.214-.146-.682-.35-.77-.595l-.013-.014c.146-.013.32-.066.46-.106.227-.06.435-.047.67-.106.106-.027.213-.06.32-.094v-.06c-.12-.12-.21-.283-.334-.395a8.867 8.867 0 00-1.104-.823c-.21-.134-.476-.22-.697-.334-.08-.04-.214-.06-.26-.127-.12-.146-.19-.34-.275-.514a17.69 17.69 0 01-.547-1.163c-.12-.262-.193-.523-.34-.763-.69-1.137-1.437-1.826-2.586-2.5-.247-.14-.543-.2-.856-.274-.167-.008-.334-.02-.5-.027-.11-.047-.216-.174-.31-.235-.38-.24-1.364-.76-1.644-.072-.18.434.267.862.422 1.082.115.153.26.328.34.5.047.116.06.235.107.356.106.294.207.622.347.897.073.14.153.287.247.413.054.073.146.107.167.227-.094.136-.1.334-.154.5-.24.757-.146 1.693.194 2.25.107.166.362.534.703.393.3-.12.234-.5.32-.835.02-.08.007-.133.048-.187v.015c.094.188.188.367.274.555.206.328.566.668.867.895.16.12.287.328.487.402v-.02h-.015c-.043-.058-.1-.086-.154-.133a3.445 3.445 0 01-.35-.4 8.76 8.76 0 01-.747-1.218c-.11-.21-.202-.436-.29-.643-.04-.08-.04-.2-.107-.24-.1.146-.247.273-.32.453-.127.288-.14.642-.188 1.01-.027.007-.014 0-.027.014-.214-.052-.287-.274-.367-.46-.2-.475-.233-1.238-.06-1.785.047-.14.247-.582.167-.716-.042-.127-.174-.2-.247-.303a2.478 2.478 0 01-.24-.427c-.16-.374-.24-.788-.414-1.162-.08-.173-.22-.354-.334-.513-.127-.18-.267-.307-.368-.52-.033-.073-.08-.194-.027-.274.014-.054.042-.075.094-.09.088-.072.335.022.422.062.247.1.455.194.662.334.094.066.195.193.315.226h.14c.214.047.455.014.655.073.355.114.675.28.962.46a5.953 5.953 0 012.085 2.286c.08.154.115.295.188.455.14.33.313.663.455.982.14.315.275.636.476.897.1.14.502.213.682.286.133.06.34.115.46.188.23.14.454.3.67.454.11.076.443.243.463.378z"},child:[]}]})(e)}function Cc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M11.998,24c-0.321,0-0.641-0.084-0.922-0.247l-2.936-1.737c-0.438-0.245-0.224-0.332-0.08-0.383 c0.585-0.203,0.703-0.25,1.328-0.604c0.065-0.037,0.151-0.023,0.218,0.017l2.256,1.339c0.082,0.045,0.197,0.045,0.272,0l8.795-5.076 c0.082-0.047,0.134-0.141,0.134-0.238V6.921c0-0.099-0.053-0.192-0.137-0.242l-8.791-5.072c-0.081-0.047-0.189-0.047-0.271,0 L3.075,6.68C2.99,6.729,2.936,6.825,2.936,6.921v10.15c0,0.097,0.054,0.189,0.139,0.235l2.409,1.392 c1.307,0.654,2.108-0.116,2.108-0.89V7.787c0-0.142,0.114-0.253,0.256-0.253h1.115c0.139,0,0.255,0.112,0.255,0.253v10.021 c0,1.745-0.95,2.745-2.604,2.745c-0.508,0-0.909,0-2.026-0.551L2.28,18.675c-0.57-0.329-0.922-0.945-0.922-1.604V6.921 c0-0.659,0.353-1.275,0.922-1.603l8.795-5.082c0.557-0.315,1.296-0.315,1.848,0l8.794,5.082c0.57,0.329,0.924,0.944,0.924,1.603 v10.15c0,0.659-0.354,1.273-0.924,1.604l-8.794,5.078C12.643,23.916,12.324,24,11.998,24z M19.099,13.993 c0-1.9-1.284-2.406-3.987-2.763c-2.731-0.361-3.009-0.548-3.009-1.187c0-0.528,0.235-1.233,2.258-1.233 c1.807,0,2.473,0.389,2.747,1.607c0.024,0.115,0.129,0.199,0.247,0.199h1.141c0.071,0,0.138-0.031,0.186-0.081 c0.048-0.054,0.074-0.123,0.067-0.196c-0.177-2.098-1.571-3.076-4.388-3.076c-2.508,0-4.004,1.058-4.004,2.833 c0,1.925,1.488,2.457,3.895,2.695c2.88,0.282,3.103,0.703,3.103,1.269c0,0.983-0.789,1.402-2.642,1.402 c-2.327,0-2.839-0.584-3.011-1.742c-0.02-0.124-0.126-0.215-0.253-0.215h-1.137c-0.141,0-0.254,0.112-0.254,0.253 c0,1.482,0.806,3.248,4.655,3.248C17.501,17.007,19.099,15.91,19.099,13.993z"},child:[]}]})(e)}function Nc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M4.459 4.208c.746.606 1.026.56 2.428.466l13.215-.793c.28 0 .047-.28-.046-.326L17.86 1.968c-.42-.326-.981-.7-2.055-.607L3.01 2.295c-.466.046-.56.28-.374.466zm.793 3.08v13.904c0 .747.373 1.027 1.214.98l14.523-.84c.841-.046.935-.56.935-1.167V6.354c0-.606-.233-.933-.748-.887l-15.177.887c-.56.047-.747.327-.747.933zm14.337.745c.093.42 0 .84-.42.888l-.7.14v10.264c-.608.327-1.168.514-1.635.514-.748 0-.935-.234-1.495-.933l-4.577-7.186v6.952L12.21 19s0 .84-1.168.84l-3.222.186c-.093-.186 0-.653.327-.746l.84-.233V9.854L7.822 9.76c-.094-.42.14-1.026.793-1.073l3.456-.233 4.764 7.279v-6.44l-1.215-.139c-.093-.514.28-.887.747-.933zM1.936 1.035l13.31-.98c1.634-.14 2.055-.047 3.082.7l4.249 2.986c.7.513.934.653.934 1.213v16.378c0 1.026-.373 1.634-1.68 1.726l-15.458.934c-.98.047-1.448-.093-1.962-.747l-3.129-4.06c-.56-.747-.793-1.306-.793-1.96V2.667c0-.839.374-1.54 1.447-1.632z"},child:[]}]})(e)}function Sc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M23.5594 14.7228a.5269.5269 0 0 0-.0563-.1191c-.139-.2632-.4768-.3418-1.0074-.2321-1.6533.3411-2.2935.1312-2.5256-.0191 1.342-2.0482 2.445-4.522 3.0411-6.8297.2714-1.0507.7982-3.5237.1222-4.7316a1.5641 1.5641 0 0 0-.1509-.235C21.6931.9086 19.8007.0248 17.5099.0005c-1.4947-.0158-2.7705.3461-3.1161.4794a9.449 9.449 0 0 0-.5159-.0816 8.044 8.044 0 0 0-1.3114-.1278c-1.1822-.0184-2.2038.2642-3.0498.8406-.8573-.3211-4.7888-1.645-7.2219.0788C.9359 2.1526.3086 3.8733.4302 6.3043c.0409.818.5069 3.334 1.2423 5.7436.4598 1.5065.9387 2.7019 1.4334 3.582.553.9942 1.1259 1.5933 1.7143 1.7895.4474.1491 1.1327.1441 1.8581-.7279.8012-.9635 1.5903-1.8258 1.9446-2.2069.4351.2355.9064.3625 1.39.3772a.0569.0569 0 0 0 .0004.0041 11.0312 11.0312 0 0 0-.2472.3054c-.3389.4302-.4094.5197-1.5002.7443-.3102.064-1.1344.2339-1.1464.8115-.0025.1224.0329.2309.0919.3268.2269.4231.9216.6097 1.015.6331 1.3345.3335 2.5044.092 3.3714-.6787-.017 2.231.0775 4.4174.3454 5.0874.2212.5529.7618 1.9045 2.4692 1.9043.2505 0 .5263-.0291.8296-.0941 1.7819-.3821 2.5557-1.1696 2.855-2.9059.1503-.8707.4016-2.8753.5388-4.1012.0169-.0703.0357-.1207.057-.1362.0007-.0005.0697-.0471.4272.0307a.3673.3673 0 0 0 .0443.0068l.2539.0223.0149.001c.8468.0384 1.9114-.1426 2.5312-.4308.6438-.2988 1.8057-1.0323 1.5951-1.6698zM2.371 11.8765c-.7435-2.4358-1.1779-4.8851-1.2123-5.5719-.1086-2.1714.4171-3.6829 1.5623-4.4927 1.8367-1.2986 4.8398-.5408 6.108-.13-.0032.0032-.0066.0061-.0098.0094-2.0238 2.044-1.9758 5.536-1.9708 5.7495-.0002.0823.0066.1989.0162.3593.0348.5873.0996 1.6804-.0735 2.9184-.1609 1.1504.1937 2.2764.9728 3.0892.0806.0841.1648.1631.2518.2374-.3468.3714-1.1004 1.1926-1.9025 2.1576-.5677.6825-.9597.5517-1.0886.5087-.3919-.1307-.813-.5871-1.2381-1.3223-.4796-.839-.9635-2.0317-1.4155-3.5126zm6.0072 5.0871c-.1711-.0428-.3271-.1132-.4322-.1772.0889-.0394.2374-.0902.4833-.1409 1.2833-.2641 1.4815-.4506 1.9143-1.0002.0992-.126.2116-.2687.3673-.4426a.3549.3549 0 0 0 .0737-.1298c.1708-.1513.2724-.1099.4369-.0417.156.0646.3078.26.3695.4752.0291.1016.0619.2945-.0452.4444-.9043 1.2658-2.2216 1.2494-3.1676 1.0128zm2.094-3.988-.0525.141c-.133.3566-.2567.6881-.3334 1.003-.6674-.0021-1.3168-.2872-1.8105-.8024-.6279-.6551-.9131-1.5664-.7825-2.5004.1828-1.3079.1153-2.4468.079-3.0586-.005-.0857-.0095-.1607-.0122-.2199.2957-.2621 1.6659-.9962 2.6429-.7724.4459.1022.7176.4057.8305.928.5846 2.7038.0774 3.8307-.3302 4.7363-.084.1866-.1633.3629-.2311.5454zm7.3637 4.5725c-.0169.1768-.0358.376-.0618.5959l-.146.4383a.3547.3547 0 0 0-.0182.1077c-.0059.4747-.054.6489-.115.8693-.0634.2292-.1353.4891-.1794 1.0575-.11 1.4143-.8782 2.2267-2.4172 2.5565-1.5155.3251-1.7843-.4968-2.0212-1.2217a6.5824 6.5824 0 0 0-.0769-.2266c-.2154-.5858-.1911-1.4119-.1574-2.5551.0165-.5612-.0249-1.9013-.3302-2.6462.0044-.2932.0106-.5909.019-.8918a.3529.3529 0 0 0-.0153-.1126 1.4927 1.4927 0 0 0-.0439-.208c-.1226-.4283-.4213-.7866-.7797-.9351-.1424-.059-.4038-.1672-.7178-.0869.067-.276.1831-.5875.309-.9249l.0529-.142c.0595-.16.134-.3257.213-.5012.4265-.9476 1.0106-2.2453.3766-5.1772-.2374-1.0981-1.0304-1.6343-2.2324-1.5098-.7207.0746-1.3799.3654-1.7088.5321a5.6716 5.6716 0 0 0-.1958.1041c.0918-1.1064.4386-3.1741 1.7357-4.4823a4.0306 4.0306 0 0 1 .3033-.276.3532.3532 0 0 0 .1447-.0644c.7524-.5706 1.6945-.8506 2.802-.8325.4091.0067.8017.0339 1.1742.081 1.939.3544 3.2439 1.4468 4.0359 2.3827.8143.9623 1.2552 1.9315 1.4312 2.4543-1.3232-.1346-2.2234.1268-2.6797.779-.9926 1.4189.543 4.1729 1.2811 5.4964.1353.2426.2522.4522.2889.5413.2403.5825.5515.9713.7787 1.2552.0696.087.1372.1714.1885.245-.4008.1155-1.1208.3825-1.0552 1.717-.0123.1563-.0423.4469-.0834.8148-.0461.2077-.0702.4603-.0994.7662zm.8905-1.6211c-.0405-.8316.2691-.9185.5967-1.0105a2.8566 2.8566 0 0 0 .135-.0406 1.202 1.202 0 0 0 .1342.103c.5703.3765 1.5823.4213 3.0068.1344-.2016.1769-.5189.3994-.9533.6011-.4098.1903-1.0957.333-1.7473.3636-.7197.0336-1.0859-.0807-1.1721-.151zm.5695-9.2712c-.0059.3508-.0542.6692-.1054 1.0017-.055.3576-.112.7274-.1264 1.1762-.0142.4368.0404.8909.0932 1.3301.1066.887.216 1.8003-.2075 2.7014a3.5272 3.5272 0 0 1-.1876-.3856c-.0527-.1276-.1669-.3326-.3251-.6162-.6156-1.1041-2.0574-3.6896-1.3193-4.7446.3795-.5427 1.3408-.5661 2.1781-.463zm.2284 7.0137a12.3762 12.3762 0 0 0-.0853-.1074l-.0355-.0444c.7262-1.1995.5842-2.3862.4578-3.4385-.0519-.4318-.1009-.8396-.0885-1.2226.0129-.4061.0666-.7543.1185-1.0911.0639-.415.1288-.8443.1109-1.3505.0134-.0531.0188-.1158.0118-.1902-.0457-.4855-.5999-1.938-1.7294-3.253-.6076-.7073-1.4896-1.4972-2.6889-2.0395.5251-.1066 1.2328-.2035 2.0244-.1859 2.0515.0456 3.6746.8135 4.8242 2.2824a.908.908 0 0 1 .0667.1002c.7231 1.3556-.2762 6.2751-2.9867 10.5405zm-8.8166-6.1162c-.025.1794-.3089.4225-.6211.4225a.5821.5821 0 0 1-.0809-.0056c-.1873-.026-.3765-.144-.5059-.3156-.0458-.0605-.1203-.178-.1055-.2844.0055-.0401.0261-.0985.0925-.1488.1182-.0894.3518-.1226.6096-.0867.3163.0441.6426.1938.6113.4186zm7.9305-.4114c.0111.0792-.049.201-.1531.3102-.0683.0717-.212.1961-.4079.2232a.5456.5456 0 0 1-.075.0052c-.2935 0-.5414-.2344-.5607-.3717-.024-.1765.2641-.3106.5611-.352.297-.0414.6111.0088.6356.1851z"},child:[]}]})(e)}function Tc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M13.527.099C6.955-.744.942 3.9.099 10.473c-.843 6.572 3.8 12.584 10.373 13.428 6.573.843 12.587-3.801 13.428-10.374C24.744 6.955 20.101.943 13.527.099zm2.471 7.485a.855.855 0 0 0-.593.25l-4.453 4.453-.307-.307-.643-.643c4.389-4.376 5.18-4.418 5.996-3.753zm-4.863 4.861l4.44-4.44a.62.62 0 1 1 .847.903l-4.699 4.125-.588-.588zm.33.694l-1.1.238a.06.06 0 0 1-.067-.032.06.06 0 0 1 .01-.073l.645-.645.512.512zm-2.803-.459l1.172-1.172.879.878-1.979.426a.074.074 0 0 1-.085-.039.072.072 0 0 1 .013-.093zm-3.646 6.058a.076.076 0 0 1-.069-.083.077.077 0 0 1 .022-.046h.002l.946-.946 1.222 1.222-2.123-.147zm2.425-1.256a.228.228 0 0 0-.117.256l.203.865a.125.125 0 0 1-.211.117h-.003l-.934-.934-.294-.295 3.762-3.758 1.82-.393.874.874c-1.255 1.102-2.971 2.201-5.1 3.268zm5.279-3.428h-.002l-.839-.839 4.699-4.125a.952.952 0 0 0 .119-.127c-.148 1.345-2.029 3.245-3.977 5.091zm3.657-6.46l-.003-.002a1.822 1.822 0 0 1 2.459-2.684l-1.61 1.613a.119.119 0 0 0 0 .169l1.247 1.247a1.817 1.817 0 0 1-2.093-.343zm2.578 0a1.714 1.714 0 0 1-.271.218h-.001l-1.207-1.207 1.533-1.533c.661.72.637 1.832-.054 2.522zM18.855 6.05a.143.143 0 0 0-.053.157.416.416 0 0 1-.053.45.14.14 0 0 0 .023.197.141.141 0 0 0 .084.03.14.14 0 0 0 .106-.05.691.691 0 0 0 .087-.751.138.138 0 0 0-.194-.033z"},child:[]}]})(e)}function Ec(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M14.23 12.004a2.236 2.236 0 0 1-2.235 2.236 2.236 2.236 0 0 1-2.236-2.236 2.236 2.236 0 0 1 2.235-2.236 2.236 2.236 0 0 1 2.236 2.236zm2.648-10.69c-1.346 0-3.107.96-4.888 2.622-1.78-1.653-3.542-2.602-4.887-2.602-.41 0-.783.093-1.106.278-1.375.793-1.683 3.264-.973 6.365C1.98 8.917 0 10.42 0 12.004c0 1.59 1.99 3.097 5.043 4.03-.704 3.113-.39 5.588.988 6.38.32.187.69.275 1.102.275 1.345 0 3.107-.96 4.888-2.624 1.78 1.654 3.542 2.603 4.887 2.603.41 0 .783-.09 1.106-.275 1.374-.792 1.683-3.263.973-6.365C22.02 15.096 24 13.59 24 12.004c0-1.59-1.99-3.097-5.043-4.032.704-3.11.39-5.587-.988-6.38-.318-.184-.688-.277-1.092-.278zm-.005 1.09v.006c.225 0 .406.044.558.127.666.382.955 1.835.73 3.704-.054.46-.142.945-.25 1.44-.96-.236-2.006-.417-3.107-.534-.66-.905-1.345-1.727-2.035-2.447 1.592-1.48 3.087-2.292 4.105-2.295zm-9.77.02c1.012 0 2.514.808 4.11 2.28-.686.72-1.37 1.537-2.02 2.442-1.107.117-2.154.298-3.113.538-.112-.49-.195-.964-.254-1.42-.23-1.868.054-3.32.714-3.707.19-.09.4-.127.563-.132zm4.882 3.05c.455.468.91.992 1.36 1.564-.44-.02-.89-.034-1.345-.034-.46 0-.915.01-1.36.034.44-.572.895-1.096 1.345-1.565zM12 8.1c.74 0 1.477.034 2.202.093.406.582.802 1.203 1.183 1.86.372.64.71 1.29 1.018 1.946-.308.655-.646 1.31-1.013 1.95-.38.66-.773 1.288-1.18 1.87-.728.063-1.466.098-2.21.098-.74 0-1.477-.035-2.202-.093-.406-.582-.802-1.204-1.183-1.86-.372-.64-.71-1.29-1.018-1.946.303-.657.646-1.313 1.013-1.954.38-.66.773-1.286 1.18-1.868.728-.064 1.466-.098 2.21-.098zm-3.635.254c-.24.377-.48.763-.704 1.16-.225.39-.435.782-.635 1.174-.265-.656-.49-1.31-.676-1.947.64-.15 1.315-.283 2.015-.386zm7.26 0c.695.103 1.365.23 2.006.387-.18.632-.405 1.282-.66 1.933-.2-.39-.41-.783-.64-1.174-.225-.392-.465-.774-.705-1.146zm3.063.675c.484.15.944.317 1.375.498 1.732.74 2.852 1.708 2.852 2.476-.005.768-1.125 1.74-2.857 2.475-.42.18-.88.342-1.355.493-.28-.958-.646-1.956-1.1-2.98.45-1.017.81-2.01 1.085-2.964zm-13.395.004c.278.96.645 1.957 1.1 2.98-.45 1.017-.812 2.01-1.086 2.964-.484-.15-.944-.318-1.37-.5-1.732-.737-2.852-1.706-2.852-2.474 0-.768 1.12-1.742 2.852-2.476.42-.18.88-.342 1.356-.494zm11.678 4.28c.265.657.49 1.312.676 1.948-.64.157-1.316.29-2.016.39.24-.375.48-.762.705-1.158.225-.39.435-.788.636-1.18zm-9.945.02c.2.392.41.783.64 1.175.23.39.465.772.705 1.143-.695-.102-1.365-.23-2.006-.386.18-.63.406-1.282.66-1.933zM17.92 16.32c.112.493.2.968.254 1.423.23 1.868-.054 3.32-.714 3.708-.147.09-.338.128-.563.128-1.012 0-2.514-.807-4.11-2.28.686-.72 1.37-1.536 2.02-2.44 1.107-.118 2.154-.3 3.113-.54zm-11.83.01c.96.234 2.006.415 3.107.532.66.905 1.345 1.727 2.035 2.446-1.595 1.483-3.092 2.295-4.11 2.295-.22-.005-.406-.05-.553-.132-.666-.38-.955-1.834-.73-3.703.054-.46.142-.944.25-1.438zm4.56.64c.44.02.89.034 1.345.034.46 0 .915-.01 1.36-.034-.44.572-.895 1.095-1.345 1.565-.455-.47-.91-.993-1.36-1.565z"},child:[]}]})(e)}function kc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M21.678.521c-1.032-.92-2.28-.55-3.513.544a8.71 8.71 0 0 0-.547.535c-2.109 2.237-4.066 6.38-4.674 9.544.237.48.422 1.093.544 1.561a13.044 13.044 0 0 1 .164.703s-.019-.071-.096-.296l-.05-.146a1.689 1.689 0 0 0-.033-.08c-.138-.32-.518-.995-.686-1.289-.143.423-.27.818-.376 1.176.484.884.778 2.4.778 2.4s-.025-.099-.147-.442c-.107-.303-.644-1.244-.772-1.464-.217.804-.304 1.346-.226 1.478.152.256.296.698.422 1.186.286 1.1.485 2.44.485 2.44l.017.224a22.41 22.41 0 0 0 .056 2.748c.095 1.146.273 2.13.5 2.657l.155-.084c-.334-1.038-.47-2.399-.41-3.967.09-2.398.642-5.29 1.661-8.304 1.723-4.55 4.113-8.201 6.3-9.945-1.993 1.8-4.692 7.63-5.5 9.788-.904 2.416-1.545 4.684-1.931 6.857.666-2.037 2.821-2.912 2.821-2.912s1.057-1.304 2.292-3.166c-.74.169-1.955.458-2.362.629-.6.251-.762.337-.762.337s1.945-1.184 3.613-1.72C21.695 7.9 24.195 2.767 21.678.521m-18.573.543A1.842 1.842 0 0 0 1.27 2.9v16.608a1.84 1.84 0 0 0 1.835 1.834h9.418a22.953 22.953 0 0 1-.052-2.707c-.006-.062-.011-.141-.016-.2a27.01 27.01 0 0 0-.473-2.378c-.121-.47-.275-.898-.369-1.057-.116-.197-.098-.31-.097-.432 0-.12.015-.245.037-.386a9.98 9.98 0 0 1 .234-1.045l.217-.028c-.017-.035-.014-.065-.031-.097l-.041-.381a32.8 32.8 0 0 1 .382-1.194l.2-.019c-.008-.016-.01-.038-.018-.053l-.043-.316c.63-3.28 2.587-7.443 4.8-9.791.066-.069.133-.128.198-.194Z"},child:[]}]})(e)}function Pc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M24 20.753v-6.306c-3.285 1.296-7.362 2.556-12.23 3.786-4.534 1.145-8.458 1.97-11.77 2.475v.045h24zM0 14.078v5.133c3.738-.55 7.116-1.206 10.13-1.967 2.962-.748 5.245-1.475 6.847-2.18 1.602-.703 2.34-1.297 2.22-1.78-.107-.42-.846-.635-2.217-.645-.703.01-1.67.06-2.904.15-1.236.09-2.774.234-4.61.426-2.85.304-5.216.537-7.1.694-.896.075-1.685.132-2.366.17zm1.035 2.95c.06 0 .114.025.16.07.046.046.07.103.07.166 0 .066-.024.12-.07.168-.047.045-.104.066-.164.066-.032 0-.064-.006-.092-.018-.03-.012-.054-.03-.075-.05-.023-.014-.04-.044-.05-.074 0-.015-.016-.045-.016-.09 0-.06.03-.12.075-.165s.105-.06.18-.06zm.81 0c.063 0 .117.025.165.07.045.046.066.103.066.166 0 .066-.022.12-.067.168-.06.045-.106.066-.18.066-.03 0-.06-.006-.09-.018s-.06-.03-.076-.05c-.03-.014-.045-.044-.06-.074-.015-.015-.015-.045-.015-.09 0-.06.014-.12.06-.165s.104-.06.164-.06zm-.81-1.51c.06 0 .114.022.16.07.046.045.07.1.07.165 0 .064-.024.12-.07.165s-.1.07-.164.07c-.065 0-.122-.024-.167-.07-.045-.045-.07-.102-.07-.165 0-.067.016-.123.06-.168s.106-.068.166-.068zm.81 0c.063 0 .117.022.165.07.045.045.066.1.066.165 0 .064-.022.12-.067.165-.06.045-.106.07-.18.07s-.12-.024-.166-.07c-.045-.045-.075-.102-.075-.165 0-.067.014-.123.06-.168s.104-.068.164-.068zM24 4.597V9.41c-1.635.1-3.68.277-6.138.534-2.49.27-4.52.48-6.093.615-1.576.15-2.713.226-3.41.24-1.363.03-2.09-.15-2.195-.554-.105-.45.705-1.05 2.445-1.77 1.74-.735 4.05-1.47 6.9-2.19 2.505-.63 5.34-1.185 8.49-1.65zm-.855-1.35c-3.255.605-6.627 1.35-10.114 2.23C7.587 6.852 3.244 8.22 0 9.573V3.248h23.146z"},child:[]}]})(e)}function Mc(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M1.125 0C.502 0 0 .502 0 1.125v21.75C0 23.498.502 24 1.125 24h21.75c.623 0 1.125-.502 1.125-1.125V1.125C24 .502 23.498 0 22.875 0zm17.363 9.75c.612 0 1.154.037 1.627.111a6.38 6.38 0 0 1 1.306.34v2.458a3.95 3.95 0 0 0-.643-.361 5.093 5.093 0 0 0-.717-.26 5.453 5.453 0 0 0-1.426-.2c-.3 0-.573.028-.819.086a2.1 2.1 0 0 0-.623.242c-.17.104-.3.229-.393.374a.888.888 0 0 0-.14.49c0 .196.053.373.156.529.104.156.252.304.443.444s.423.276.696.41c.273.135.582.274.926.416.47.197.892.407 1.266.628.374.222.695.473.963.753.268.279.472.598.614.957.142.359.214.776.214 1.253 0 .657-.125 1.21-.373 1.656a3.033 3.033 0 0 1-1.012 1.085 4.38 4.38 0 0 1-1.487.596c-.566.12-1.163.18-1.79.18a9.916 9.916 0 0 1-1.84-.164 5.544 5.544 0 0 1-1.512-.493v-2.63a5.033 5.033 0 0 0 3.237 1.2c.333 0 .624-.03.872-.09.249-.06.456-.144.623-.25.166-.108.29-.234.373-.38a1.023 1.023 0 0 0-.074-1.089 2.12 2.12 0 0 0-.537-.5 5.597 5.597 0 0 0-.807-.444 27.72 27.72 0 0 0-1.007-.436c-.918-.383-1.602-.852-2.053-1.405-.45-.553-.676-1.222-.676-2.005 0-.614.123-1.141.369-1.582.246-.441.58-.804 1.004-1.089a4.494 4.494 0 0 1 1.47-.629 7.536 7.536 0 0 1 1.77-.201zm-15.113.188h9.563v2.166H9.506v9.646H6.789v-9.646H3.375z"},child:[]}]})(e)}function Ac(e){return F({tag:"svg",attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M4.345 7.053c-.495.02-.44.725-.536 1.081-.157.583-.3 1.325-.347 1.926-.046.585-.008 1.127.066 1.719.058.46.191.767.07.89-.108.11-3.216 2.962-3.466 3.123-.26.169-.08.584.069.817.157.246.23.373.557.33.306-.042.405-.409.583-.606.228-.252 2.421-2.401 2.616-2.401.077.544.367 1.064.67 1.513.15.222.314.439.505.629.175.175.4.317.587.45.44.024.795-.301.35-.67-.17-.14-.735-.971-.927-1.43-.18-.43-.574-1.076-.146-1.428 1.494-1.23 3.72-2.262 4.247-2.313-.257 1.024-1.356 3.048-1.757 4.012-.14.333-.231.732-.185 1.094.055.434.383.774.587.806.417-.023.7-.387.946-.645.343-.357.634-.685.974-1.043.339-.356.672-.731.971-1.07.184-.207.674-.713.963-.713-.11.693-.716 1.552-.839 2.254-.125.716.531 1.596 1.217.956.623-.58 1.255-1.129 1.867-1.72.217-.208.175.037.224.242.05.208.176.91.275 1.1.18.346.496.592.897.598.362.006.727-.161.982-.414.19-.187.513-.699.154-.832-.23-.086-.217-.176-.495-.129-.172.029-.362.074-.507.179-.367-.003-.381-.89-.324-1.161.068-.327.207-.659.185-.998-.026-.418-.478-.69-.582-.72-.156-.076-.253.023-.458.212-.173.161-.363.332-.535.495-.34.322-.768.813-.942.813.305-.705.708-2.652-.643-2.48-.563.071-.95.377-1.394.71-.29.28-.683.641-.936.87-.236.216-.371.404-.496.404.132-.747 1.685-3.167.885-3.853-.158-.136-.313-.325-.515-.349a4.637 4.637 0 0 0-.833.19c-.565.18-2.78 1.28-4.19 2.289-.131.094-.214-.085-.231-.29-.087-1.058.199-2.19.496-3.188.208-.696-.557-1.225-.659-1.249zm18.177.874c-.166.364-.2.894-.248 1.319a24.307 24.307 0 0 0-1.246-.115c.238.296.691.588 1.056.724-.048.366-.434.67-.599 1.021.458-.127.676-.47.989-.821.362.22.791.627 1.26.636-.177-.376-.334-.695-.658-.966.269-.175.717-.362.924-.633-.345-.074-.718-.093-1.052-.015-.258-.284-.3-.772-.426-1.15zm-2.92.079c-.23.02-.613.49-.832.773-.807 1.039-1.542 3.15-1.661 3.542-.363 1.195-.502 2.672.28 3.722.456.612 1.258.66 2.041.434.405-.116.812-.406.95-.723.114-.263.174-.753-.404-.38-.224.145-.634.304-1.37.291-.247-.004-.651-.357-.76-.722-.192-.595-.11-1.393-.11-1.393.167-1.028.642-2.146 1.061-3.076.163-.36.658-1.259.842-1.546 0 0 .239-.373.131-.77-.031-.116-.091-.16-.168-.152zm3.072 2.976c-.12.264-.144.648-.18.956-.274-.031-.63-.066-.904-.083.172.215.501.426.766.525-.034.265-.314.486-.434.741.332-.092.49-.34.717-.596.263.16.575.456.914.462-.127-.273-.242-.504-.477-.701.195-.127.52-.262.67-.46a1.77 1.77 0 0 0-.763-.01c-.187-.206-.217-.56-.309-.834zm-1.123 2.422c-.083.183-.1.449-.125.662a12.6 12.6 0 0 0-.624-.058c.119.148.346.295.53.363-.025.184-.219.336-.301.513.23-.064.339-.236.496-.413.181.11.397.316.632.32-.088-.19-.168-.349-.33-.485.135-.087.36-.181.463-.317a1.22 1.22 0 0 0-.527-.008c-.13-.142-.151-.387-.214-.576z"},child:[]}]})(e)}function Rc(e){return F({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M392.3 32H56.1C25.1 32 0 57.1 0 88c-.1 0 0-4 0 336 0 30.9 25.1 56 56 56h336.2c30.8-.2 55.7-25.2 55.7-56V88c.1-30.8-24.8-55.8-55.6-56zM197 371.3c-.2 14.7-12.1 26.6-26.9 26.6H87.4c-14.8.1-26.9-11.8-27-26.6V117.1c0-14.8 12-26.9 26.9-26.9h82.9c14.8 0 26.9 12 26.9 26.9v254.2zm193.1-112c0 14.8-12 26.9-26.9 26.9h-81c-14.8 0-26.9-12-26.9-26.9V117.2c0-14.8 12-26.9 26.8-26.9h81.1c14.8 0 26.9 12 26.9 26.9v142.1z"},child:[]}]})(e)}function Ge(e){return F({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M448 73.143v45.714C448 159.143 347.667 192 224 192S0 159.143 0 118.857V73.143C0 32.857 100.333 0 224 0s224 32.857 224 73.143zM448 176v102.857C448 319.143 347.667 352 224 352S0 319.143 0 278.857V176c48.125 33.143 136.208 48.572 224 48.572S399.874 209.143 448 176zm0 160v102.857C448 479.143 347.667 512 224 512S0 479.143 0 438.857V336c48.125 33.143 136.208 48.572 224 48.572S399.874 369.143 448 336z"},child:[]}]})(e)}function zc(e){return F({tag:"svg",attr:{viewBox:"0 0 16 16",fill:"currentColor"},child:[{tag:"path",attr:{fillRule:"evenodd",clipRule:"evenodd",d:"M6 2.984V2h-.09c-.313 0-.616.062-.909.185a2.33 2.33 0 0 0-.775.53 2.23 2.23 0 0 0-.493.753v.001a3.542 3.542 0 0 0-.198.83v.002a6.08 6.08 0 0 0-.024.863c.*************.018.869 0 .203-.04.393-.117.572v.001a1.504 1.504 0 0 1-.765.787 1.376 1.376 0 0 1-.558.115H2v.984h.09c.195 0 .38.04.556.121l.001.001c.178.078.329.184.455.318l.002.002c.13.13.233.285.307.465l.001.002c.078.18.117.368.117.566 0 .29-.006.58-.018.869-.012.296-.004.585.024.87v.001c.033.283.099.558.197.824v.001c.106.273.271.524.494.753.223.23.482.407.775.53.293.123.596.185.91.185H6v-.984h-.09c-.2 0-.387-.038-.563-.115a1.613 1.613 0 0 1-.457-.32 1.659 1.659 0 0 1-.309-.467c-.074-.18-.11-.37-.11-.573 0-.228.003-.453.011-.672.008-.228.008-.45 0-.665a4.639 4.639 0 0 0-.055-.64 2.682 2.682 0 0 0-.168-.609A2.284 2.284 0 0 0 3.522 8a2.284 2.284 0 0 0 .738-.955c.08-.192.135-.393.168-.602.033-.21.051-.423.055-.64.008-.22.008-.442 0-.666-.008-.224-.012-.45-.012-.678a1.47 1.47 0 0 1 .877-1.354 1.33 1.33 0 0 1 .563-.121H6zm4 10.032V14h.09c.313 0 .616-.062.909-.185.293-.123.552-.3.775-.53.223-.23.388-.48.493-.753v-.001c.1-.266.165-.543.198-.83v-.002c.028-.28.036-.567.024-.863-.012-.29-.018-.58-.018-.869 0-.203.04-.393.117-.572v-.001a1.502 1.502 0 0 1 .765-.787 1.38 1.38 0 0 1 .558-.115H14v-.984h-.09c-.196 0-.381-.04-.557-.121l-.001-.001a1.376 1.376 0 0 1-.455-.318l-.002-.002a1.415 1.415 0 0 1-.307-.465v-.002a1.405 1.405 0 0 1-.118-.566c0-.29.006-.58.018-.869a6.174 6.174 0 0 0-.024-.87v-.001a3.537 3.537 0 0 0-.197-.824v-.001a2.23 2.23 0 0 0-.494-.753 2.331 2.331 0 0 0-.775-.53 2.325 2.325 0 0 0-.91-.185H10v.984h.09c.2 0 .387.038.562.115.174.082.326.188.457.32.127.134.23.29.309.467.074.18.11.37.11.573 0 .228-.003.452-.011.672-.008.228-.008.45 0 .665.004.222.022.435.055.64.033.214.089.416.168.609a2.285 2.285 0 0 0 .738.955 2.285 2.285 0 0 0-.738.955 2.689 2.689 0 0 0-.168.602c-.033.21-.051.423-.055.64a9.15 9.15 0 0 0 0 .666c.008.224.012.45.012.678a1.471 1.471 0 0 1-.877 1.354 1.33 1.33 0 0 1-.563.121H10z"},child:[]}]})(e)}function Ic(e){return F({tag:"svg",attr:{viewBox:"0 0 16 16",fill:"currentColor"},child:[{tag:"path",attr:{d:"M10.8634 13.9195C10.6568 14.0195 10.4233 14.0246 10.2185 13.9444C10.1162 13.9044 10.021 13.843 9.93997 13.7614L4.81616 9.06268L2.58433 10.7656C2.37657 10.9241 2.08597 10.9111 1.89301 10.7347L1.17719 10.0802C0.941168 9.86437 0.940898 9.49112 1.17661 9.27496L3.11213 7.5L1.17661 5.72504C0.940898 5.50888 0.941168 5.13563 1.17719 4.91982L1.89301 4.2653C2.08597 4.08887 2.37657 4.07588 2.58433 4.2344L4.81616 5.93732L9.93997 1.23855C9.97037 1.20797 10.0028 1.18023 10.0368 1.15538C10.2748 0.981429 10.5922 0.949298 10.8634 1.08048L13.5399 2.37507C13.8212 2.5111 14 2.79721 14 3.11109V8H10.752V4.53356L6.86419 7.5L10.752 10.4664V8H14V11.8889C14 12.2028 13.8211 12.4889 13.5399 12.625L10.8634 13.9195Z"},child:[]}]})(e)}function Dc(e){return F({tag:"svg",attr:{version:"1.1",viewBox:"0 0 32 32"},child:[{tag:"path",attr:{d:"M16 5.343c-6.196 0-11.219 5.023-11.219 11.219 0 4.957 3.214 9.162 7.673 10.645 0.561 0.103 0.766-0.244 0.766-0.54 0-0.267-0.010-1.152-0.016-2.088-3.12 0.678-3.779-1.323-3.779-1.323-0.511-1.296-1.246-1.641-1.246-1.641-1.020-0.696 0.077-0.682 0.077-0.682 1.126 0.078 1.72 1.156 1.72 1.156 1.001 1.715 2.627 1.219 3.265 0.931 0.102-0.723 0.392-1.219 0.712-1.498-2.49-0.283-5.11-1.246-5.11-5.545 0-1.226 0.438-2.225 1.154-3.011-0.114-0.285-0.501-1.426 0.111-2.97 0 0 0.941-0.301 3.085 1.15 0.894-0.25 1.854-0.373 2.807-0.377 0.953 0.004 1.913 0.129 2.809 0.379 2.14-1.453 3.083-1.15 3.083-1.15 0.613 1.545 0.227 2.685 0.112 2.969 0.719 0.785 1.153 1.785 1.153 3.011 0 4.31-2.624 5.259-5.123 5.537 0.404 0.348 0.761 1.030 0.761 2.076 0 1.5-0.015 2.709-0.015 3.079 0 0.299 0.204 0.648 0.772 0.538 4.455-1.486 7.666-5.69 7.666-10.645 0-6.195-5.023-11.219-11.219-11.219z"},child:[]}]})(e)}function Oc(e){return F({tag:"svg",attr:{version:"1.1",viewBox:"0 0 32 32"},child:[{tag:"path",attr:{d:"M16.232 24.047c-0.15-0.034-0.295-0.081-0.441-0.124-0.037-0.011-0.074-0.022-0.11-0.033-0.143-0.044-0.284-0.090-0.425-0.139-0.019-0.007-0.039-0.014-0.058-0.021-0.126-0.045-0.251-0.091-0.375-0.139-0.035-0.014-0.070-0.027-0.105-0.041-0.136-0.054-0.271-0.11-0.405-0.168-0.027-0.012-0.054-0.024-0.081-0.036-0.115-0.052-0.228-0.105-0.341-0.159-0.033-0.016-0.065-0.031-0.099-0.047-0.089-0.043-0.177-0.090-0.264-0.134-0.059-0.031-0.118-0.060-0.176-0.092-0.107-0.058-0.212-0.117-0.317-0.178-0.035-0.020-0.071-0.038-0.107-0.059-0.139-0.081-0.277-0.166-0.412-0.252-0.037-0.024-0.074-0.050-0.111-0.074-0.099-0.063-0.197-0.128-0.293-0.195-0.032-0.021-0.063-0.045-0.094-0.066-0.093-0.066-0.186-0.132-0.277-0.2-0.042-0.031-0.082-0.062-0.123-0.093-0.084-0.064-0.168-0.129-0.25-0.196-0.037-0.030-0.075-0.060-0.112-0.090-0.105-0.087-0.209-0.173-0.312-0.263-0.011-0.009-0.023-0.018-0.034-0.028-0.111-0.097-0.22-0.197-0.328-0.298-0.031-0.030-0.062-0.059-0.092-0.088-0.080-0.076-0.158-0.153-0.235-0.231-0.031-0.031-0.062-0.061-0.092-0.092-0.098-0.101-0.194-0.203-0.289-0.306-0.005-0.005-0.010-0.010-0.014-0.015-0.1-0.109-0.197-0.221-0.293-0.334-0.026-0.031-0.051-0.060-0.077-0.091-0.071-0.086-0.142-0.173-0.211-0.261-0.026-0.031-0.052-0.064-0.077-0.096-0.083-0.108-0.164-0.215-0.243-0.324-2.197-2.996-2.986-7.129-1.23-10.523l-1.556 1.974c-1.994 2.866-1.746 6.595-0.223 9.64 0.036 0.073 0.074 0.145 0.112 0.217 0.024 0.045 0.046 0.092 0.071 0.137 0.014 0.027 0.030 0.053 0.044 0.079 0.026 0.049 0.053 0.095 0.079 0.142 0.047 0.083 0.096 0.166 0.145 0.249 0.027 0.045 0.055 0.091 0.083 0.136 0.055 0.089 0.111 0.176 0.169 0.264 0.024 0.037 0.047 0.075 0.072 0.111 0.080 0.118 0.161 0.236 0.244 0.353 0.002 0.003 0.005 0.006 0.007 0.009 0.013 0.018 0.028 0.037 0.041 0.056 0.072 0.1 0.147 0.199 0.223 0.296 0.028 0.036 0.056 0.072 0.084 0.107 0.067 0.085 0.136 0.169 0.206 0.253 0.026 0.031 0.052 0.063 0.079 0.094 0.094 0.11 0.189 0.22 0.287 0.328 0.002 0.002 0.004 0.004 0.006 0.005 0.004 0.005 0.008 0.008 0.011 0.013 0.095 0.104 0.193 0.206 0.291 0.307 0.031 0.032 0.062 0.063 0.093 0.094 0.076 0.077 0.154 0.153 0.233 0.228 0.032 0.030 0.063 0.061 0.095 0.091 0.105 0.099 0.211 0.196 0.319 0.291 0.002 0.001 0.003 0.003 0.005 0.004 0.018 0.016 0.038 0.032 0.056 0.047 0.095 0.082 0.192 0.164 0.29 0.245 0.040 0.032 0.080 0.064 0.12 0.096 0.080 0.064 0.16 0.127 0.241 0.189 0.043 0.033 0.086 0.066 0.129 0.098 0.089 0.066 0.18 0.131 0.271 0.194 0.033 0.024 0.065 0.047 0.099 0.070 0.009 0.006 0.018 0.013 0.027 0.019 0.086 0.060 0.175 0.116 0.263 0.174 0.038 0.025 0.075 0.051 0.114 0.076 0.136 0.086 0.273 0.171 0.412 0.253 0.038 0.022 0.076 0.043 0.114 0.064 0.102 0.059 0.205 0.117 0.309 0.174 0.056 0.030 0.114 0.059 0.171 0.088 0.073 0.038 0.147 0.078 0.221 0.115 0.017 0.009 0.035 0.017 0.051 0.025 0.030 0.014 0.060 0.028 0.091 0.044 0.116 0.055 0.233 0.11 0.351 0.163 0.025 0.011 0.049 0.022 0.074 0.033 0.135 0.059 0.271 0.116 0.409 0.17 0.033 0.014 0.066 0.026 0.1 0.039 0.127 0.049 0.256 0.098 0.386 0.143 0.016 0.006 0.032 0.012 0.049 0.017 0.142 0.050 0.286 0.096 0.43 0.141 0.034 0.010 0.069 0.021 0.104 0.031 0.147 0.044 0.293 0.097 0.445 0.125 9.643 1.759 12.444-5.795 12.444-5.795-2.352 3.065-6.528 3.873-10.485 2.974zM12.758 16.231c0.216 0.31 0.456 0.678 0.742 0.927 0.104 0.114 0.213 0.226 0.324 0.336 0.028 0.029 0.057 0.056 0.085 0.084 0.108 0.105 0.217 0.207 0.33 0.307 0.005 0.003 0.009 0.008 0.014 0.012 0.001 0.001 0.002 0.002 0.003 0.003 0.125 0.11 0.255 0.216 0.386 0.319 0.029 0.022 0.058 0.046 0.088 0.069 0.132 0.101 0.266 0.2 0.404 0.295 0.004 0.003 0.008 0.006 0.012 0.009 0.061 0.042 0.123 0.081 0.184 0.122 0.030 0.019 0.058 0.040 0.088 0.058 0.098 0.063 0.198 0.125 0.299 0.183 0.014 0.009 0.028 0.016 0.042 0.024 0.087 0.051 0.176 0.1 0.265 0.148 0.031 0.018 0.063 0.033 0.094 0.049 0.061 0.032 0.123 0.064 0.185 0.096 0.009 0.004 0.019 0.009 0.028 0.012 0.127 0.063 0.255 0.123 0.386 0.18 0.028 0.012 0.057 0.023 0.085 0.035 0.105 0.045 0.21 0.088 0.316 0.129 0.045 0.017 0.091 0.033 0.135 0.050 0.097 0.036 0.193 0.069 0.291 0.101 0.044 0.014 0.087 0.028 0.131 0.042 0.139 0.043 0.276 0.098 0.42 0.122 7.445 1.233 9.164-4.499 9.164-4.499-1.549 2.232-4.55 3.296-7.752 2.465-0.142-0.038-0.282-0.078-0.422-0.122-0.043-0.013-0.084-0.027-0.127-0.041-0.099-0.032-0.197-0.066-0.295-0.102-0.045-0.017-0.089-0.033-0.133-0.050-0.107-0.041-0.213-0.084-0.317-0.128-0.029-0.013-0.058-0.024-0.086-0.036-0.131-0.057-0.261-0.117-0.389-0.18-0.066-0.032-0.13-0.066-0.195-0.099-0.037-0.019-0.075-0.038-0.112-0.058-0.083-0.045-0.165-0.092-0.246-0.139-0.019-0.011-0.040-0.022-0.059-0.033-0.101-0.059-0.2-0.12-0.299-0.182-0.030-0.019-0.060-0.040-0.090-0.060-0.065-0.042-0.13-0.085-0.193-0.128-0.137-0.095-0.271-0.194-0.402-0.294-0.030-0.024-0.061-0.047-0.091-0.071-1.401-1.107-2.512-2.619-3.041-4.334-0.554-1.778-0.434-3.775 0.525-5.395l-1.178 1.663c-1.442 2.075-1.364 4.853-0.239 7.048 0.189 0.368 0.401 0.725 0.638 1.065zM20.606 13.664c0.061 0.023 0.123 0.043 0.185 0.064 0.027 0.008 0.054 0.018 0.082 0.026 0.088 0.027 0.175 0.060 0.265 0.076 4.111 0.794 5.226-2.11 5.523-2.537-0.977 1.406-2.618 1.744-4.632 1.255-0.159-0.039-0.334-0.096-0.488-0.151-0.197-0.070-0.39-0.15-0.579-0.24-0.358-0.172-0.699-0.38-1.015-0.619-1.802-1.367-2.922-3.976-1.746-6.101l-0.637 0.877c-0.85 1.251-0.933 2.805-0.344 4.186 0.622 1.467 1.897 2.617 3.384 3.163z"},child:[]}]})(e)}function Fc(e){return F({tag:"svg",attr:{version:"1.1",viewBox:"0 0 32 32"},child:[{tag:"path",attr:{d:"M17.319 9.414c-2.444 2.444-4.5 4.435-4.597 4.435-0.081 0-1.424-0.987-2.962-2.185l-2.784-2.185-2.266 1.133v11.331l2.266 1.133 2.574-2.007c1.425-1.117 2.736-2.12 2.914-2.234 0.324-0.194 0.647 0.097 4.84 4.274l4.484 4.484 2.752-1.117 2.752-1.101v-18.195l-2.104-0.842c-1.149-0.47-2.396-0.955-2.768-1.101l-0.664-0.259-4.435 4.435zM21.706 16.278c0 2.493-0.032 4.532-0.097 4.532-0.227 0-5.73-4.435-5.682-4.581 0.049-0.178 5.471-4.468 5.666-4.468 0.065-0.016 0.114 2.023 0.114 4.516zM8.837 14.659l1.619 1.619-1.619 1.619c-0.89 0.89-1.667 1.619-1.732 1.619-0.081 0-0.13-1.457-0.13-3.237s0.048-3.238 0.13-3.238c0.065 0 0.842 0.729 1.732 1.619z"},child:[]}]})(e)}const Lc={"C#":{icon:ht,color:"text-purple-600"},"VB.NET":{icon:ht,color:"text-blue-600"},"ASP.NET":{icon:ht,color:"text-blue-500"},Razor:{icon:ht,color:"text-blue-500"},"Node.js":{icon:Cc,color:"text-green-600"},"T-SQL":{icon:Ge,color:"text-blue-700"},LINQ:{icon:Ge,color:"text-purple-500"},"Web API":{icon:zc,color:"text-yellow-600"},MSSQL:{icon:Ge,color:"text-red-600"},MySQL:{icon:jc,color:"text-orange-500"},PostgreSQL:{icon:Sc,color:"text-blue-600"},SQLite:{icon:kc,color:"text-blue-500"},CosmosDB:{icon:Ge,color:"text-purple-600"},"NoSQL (ElasticSearch)":{icon:fc,color:"text-yellow-500"},"Visual Studio":{icon:Fc,color:"text-purple-600"},"VS Code":{icon:Ic,color:"text-blue-500"},DevExpress:{icon:hc,color:"text-orange-500"},Metabase:{icon:wc,color:"text-blue-600"},Postman:{icon:Tc,color:"text-orange-500"},Docker:{icon:mc,color:"text-blue-500"},".NET Framework & Core":{icon:ht,color:"text-purple-600"},"ADO.NET":{icon:Ge,color:"text-blue-600"},Dapper:{icon:Ge,color:"text-green-600"},HTML:{icon:xc,color:"text-orange-500"},CSS:{icon:uc,color:"text-blue-500"},JavaScript:{icon:bc,color:"text-yellow-500"},jQuery:{icon:Oc,color:"text-blue-600"},Angular:{icon:ic,color:"text-red-600"},React:{icon:Ec,color:"text-cyan-500"},TypeScript:{icon:Mc,color:"text-blue-600"},JSON:{icon:yc,color:"text-yellow-600"},XML:{icon:Ac,color:"text-orange-600"},TFS:{icon:pc,color:"text-blue-600"},SVN:{icon:Pc,color:"text-blue-500"},GitHub:{icon:Dc,color:"text-gray-800"},GitLab:{icon:gc,color:"text-orange-500"},Asana:{icon:lc,color:"text-red-500"},Bitbucket:{icon:cc,color:"text-blue-600"},JIRA:{icon:vc,color:"text-blue-500"},Notion:{icon:Nc,color:"text-gray-800"},Trello:{icon:Rc,color:"text-blue-500"},Confluence:{icon:dc,color:"text-blue-600"}},_c=()=>{const e=[{title:"Programming Languages & Frameworks",skills:["C#","VB.NET","ASP.NET","Razor","Node.js",".NET Framework & Core","JavaScript","TypeScript","HTML","CSS"]},{title:"Frontend Technologies",skills:["React","Angular","jQuery"]},{title:"Databases & Data Access",skills:["MSSQL","MySQL","PostgreSQL","SQLite","CosmosDB","NoSQL (ElasticSearch)","T-SQL","LINQ","ADO.NET","Dapper"]},{title:"Development Tools",skills:["Visual Studio","VS Code","DevExpress","Postman","Docker"]},{title:"Data & Analytics",skills:["Metabase","JSON","XML","Web API"]},{title:"Version Control",skills:["GitHub","GitLab","TFS","SVN","Bitbucket"]},{title:"Project Management",skills:["JIRA","Trello","Asana","Notion","Confluence"]}];return s.jsxs("div",{className:"p-4 bg-white font-tahoma",children:[s.jsxs("div",{className:"flex items-center mb-4 pb-2 border-b-2 border-blue-500",children:[s.jsx("span",{className:"text-2xl mr-2",children:"⚡"}),s.jsx("h2",{className:"text-lg font-bold text-blue-800",children:"Skills & Experience"})]}),s.jsx("div",{className:"bg-blue-50 p-3 rounded mb-4 border-l-4 border-blue-500",children:s.jsxs("p",{className:"text-sm text-blue-800",children:[s.jsx("strong",{children:"Professional Summary:"})," Experienced software developer with expertise in .NET technologies, full-stack web development, and database management. Proficient in modern frameworks and development tools."]})}),e.map(t=>s.jsxs("div",{className:"mb-6",children:[s.jsx("h3",{className:"font-bold text-blue-800 mb-3 pb-1 border-b border-gray-300 bg-gradient-to-r from-blue-100 to-transparent px-2 py-1",children:t.title}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:t.skills.map(r=>{const o=Lc[r],n=o==null?void 0:o.icon;return s.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-gray-50 rounded border hover:bg-blue-50 transition-colors",children:[n?s.jsx(n,{className:`w-4 h-4 ${o.color}`}):s.jsx("span",{className:"text-lg",children:"🔧"}),s.jsx("span",{className:"text-sm font-medium",children:r})]},r)})})]},t.title)),s.jsxs("div",{className:"mt-6 p-4 bg-gray-100 rounded border",children:[s.jsx("h3",{className:"font-bold text-gray-800 mb-2",children:"Current Focus Areas"}),s.jsxs("ul",{className:"text-sm space-y-1 text-gray-700",children:[s.jsx("li",{children:"• Cloud-native application development"}),s.jsx("li",{children:"• Microservices architecture with .NET"}),s.jsx("li",{children:"• Modern frontend frameworks (React, Angular)"}),s.jsx("li",{children:"• DevOps and containerization with Docker"}),s.jsx("li",{children:"• Database optimization and performance tuning"})]})]})]})},Bc=()=>{const e=[{category:"Core Technologies",items:[{name:"React",version:"18.3.1",description:"JavaScript library for building dynamic user interfaces"},{name:"TypeScript",version:"Latest",description:"Typed superset of JavaScript for robust, scalable applications"},{name:"React Router",version:"6.26.2",description:"Declarative routing for single-page React applications"}]},{category:"Styling & UI",items:[{name:"Tailwind CSS",version:"Latest",description:"A utility-first CSS framework for rapid UI development"},{name:"Shadcn/ui",version:"Latest",description:"Beautifully designed, accessible components built on Radix UI"},{name:"Lucide React",version:"0.462.0",description:"Beautiful & consistent icon toolkit for a clean UI"},{name:"Recharts",version:"2.12.7",description:"A composable charting library for data visualization"},{name:"Sonner",version:"1.5.0",description:"An opinionated toast component for user notifications"},{name:"React Resizable Panels",version:"2.1.3",description:"Components for creating flexible, resizable panel layouts"},{name:"React Icons",version:"5.5.0",description:"Extensive library of popular icons for React projects"}]},{category:"State Management & Forms",items:[{name:"TanStack Query",version:"5.56.2",description:"Powerful asynchronous state management and data-fetching"},{name:"React Hook Form",version:"7.53.0",description:"Performant, flexible forms with easy-to-use validation"},{name:"Zod",version:"3.23.8",description:"TypeScript-first schema validation with static type inference"}]},{category:"Build Tools & Utilities",items:[{name:"Vite",version:"Latest",description:"Next-generation frontend tooling for an optimized dev experience"},{name:"Class Variance Authority",version:"0.7.1",description:"Create type-safe, composable UI component variants"},{name:"clsx",version:"2.1.1",description:"A tiny utility for constructing conditional className strings"},{name:"Tailwind Merge",version:"2.5.2",description:"Merge Tailwind CSS classes without style conflicts"},{name:"tailwindcss-animate",version:"1.0.7",description:"A Tailwind CSS plugin for orchestrating CSS animations"}]}];return s.jsxs("div",{className:"p-4 bg-white font-tahoma",children:[s.jsxs("div",{className:"flex items-center mb-4 pb-3 border-b-2 border-blue-500",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-500 rounded mr-3 flex items-center justify-center",children:s.jsx("span",{className:"text-white text-lg",children:"💻"})}),s.jsxs("div",{children:[s.jsx("h2",{className:"text-lg font-bold text-blue-800",children:"System Information"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Portfolio Application Tech Stack"})]})]}),s.jsx("div",{className:"grid grid-cols-1 gap-4 mb-6",children:s.jsx("div",{className:"bg-blue-50 p-3 rounded border-l-4 border-blue-500",children:s.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[s.jsxs("div",{children:[s.jsx("strong",{children:"System:"})," Windows XP Portfolio Theme"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Architecture:"})," Single Page Application"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Runtime:"})," Modern Web Browsers"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Build System:"})," Vite + TypeScript"]})]})})}),e.map((t,r)=>s.jsxs("div",{className:"mb-6",children:[s.jsxs("h3",{className:"font-bold text-blue-800 mb-3 pb-1 border-b border-gray-300 bg-gradient-to-r from-blue-100 to-transparent px-2 py-1",children:["📦 ",t.category]}),s.jsx("div",{className:"space-y-2",children:t.items.map((o,n)=>s.jsxs("div",{className:"bg-gray-50 p-3 rounded border hover:bg-blue-50 transition-colors",children:[s.jsxs("div",{className:"flex justify-between items-start mb-1",children:[s.jsx("span",{className:"font-semibold text-blue-900",children:o.name}),s.jsxs("span",{className:"text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded",children:["v",o.version]})]}),s.jsx("p",{className:"text-xs text-gray-600",children:o.description})]},n))})]},r)),s.jsxs("div",{className:"mt-6 p-4 bg-gradient-to-r from-blue-100 to-purple-100 rounded border",children:[s.jsxs("h3",{className:"font-bold text-gray-800 mb-2 flex items-center",children:[s.jsx("span",{className:"mr-2",children:"⚡"}),"Performance Features"]}),s.jsxs("ul",{className:"text-sm space-y-1 text-gray-700",children:[s.jsx("li",{children:"• Hot Module Replacement (HMR) for instant updates via Vite"}),s.jsx("li",{children:"• Tree-shaking for optimized bundle size"}),s.jsx("li",{children:"• TypeScript for enhanced developer experience and type safety"}),s.jsx("li",{children:"• Component-based architecture for reusability and maintainability"}),s.jsx("li",{children:"• Utility-first styling with Tailwind for efficient, consistent design"})]})]}),s.jsx("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded",children:s.jsxs("p",{className:"text-xs text-yellow-800",children:[s.jsx("strong",{children:"Note:"})," This Windows XP themed portfolio showcases modern web technologies wrapped in nostalgic styling. All components are built with accessibility and performance in mind."]})})]})},Vc=[{id:"projects",name:"My Projects",icon:ft,type:"folder"},{id:"my_resume",name:"my_resume.pdf",icon:De,type:"file"},{id:"skills",name:"Skills & Experience",icon:ft,type:"folder"},{id:"certifications",name:"Certifications",icon:ft,type:"folder"}],Hc=({onOpenWindow:e})=>{const t=(r,o,n)=>{r.stopPropagation(),e(o,n)};return s.jsxs("div",{className:"p-4 bg-white font-tahoma h-full overflow-y-auto",children:[s.jsxs("div",{className:"flex items-center mb-4 pb-2 border-b-2 border-blue-500",children:[s.jsx(ft,{size:32,className:"text-yellow-600 mr-3"}),s.jsx("h2",{className:"text-xl font-bold text-blue-800",children:"My Documents"})]}),s.jsx("div",{className:"grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4",children:Vc.map(r=>{const o=r.icon;return s.jsxs("div",{className:"flex flex-col items-center justify-center text-center cursor-pointer p-2 rounded hover:bg-blue-100",onDoubleClick:n=>t(n,r.id,r.name),children:[s.jsx(o,{size:48,className:r.id==="my_resume"?"text-red-600":r.type==="folder"?"text-yellow-500":"text-gray-700"}),s.jsx("span",{className:"mt-2 text-xs break-words w-full",children:r.name})]},r.id)})})]})},Wc=[{src:"https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=600&q=80",alt:"Project Demo 1: A MacBook with lines of code on its screen",title:"Development Environment.jpg",size:"1.2 MB"},{src:"https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=600&q=80",alt:"Programming meme: Matrix movie still with code",title:"The Matrix of Code.gif",size:"2.5 MB"},{src:"https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=600&q=80",alt:"Project Screenshot: Java code on a monitor",title:"LegacySystemRefactor.png",size:"850 KB"},{src:"https://images.unsplash.com/photo-1487058792275-0ad4aaf24ca7?w=600&q=80",alt:"Project Screenshot: Colorful web code on a screen",title:"RainbowSyntax.png",size:"970 KB"}],$c=()=>s.jsxs("div",{className:"p-4 bg-white font-tahoma h-full overflow-y-auto",children:[s.jsxs("div",{className:"flex items-center mb-4 pb-2 border-b-2 border-blue-500",children:[s.jsx(to,{size:32,className:"text-blue-600 mr-3"}),s.jsx("h2",{className:"text-xl font-bold text-blue-800",children:"My Pictures"})]}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:Wc.map((e,t)=>s.jsxs("div",{className:"group cursor-pointer",children:[s.jsx("div",{className:"bg-gray-100 border rounded overflow-hidden aspect-square flex items-center justify-center group-hover:shadow-lg transition-shadow",children:s.jsx("img",{src:e.src,alt:e.alt,className:"w-full h-full object-cover"})}),s.jsxs("div",{className:"text-center mt-2 text-xs",children:[s.jsx("p",{className:"font-semibold truncate",children:e.title}),s.jsx("p",{className:"text-gray-500",children:e.size})]})]},t))})]}),Gc=()=>{const e=u.useRef(null),t=u.useRef(null);u.useEffect(()=>{if(window.onYouTubeIframeAPIReady=()=>{t.current&&(e.current=new window.YT.Player(t.current,{events:{}}))},window.YT&&window.YT.Player)window.onYouTubeIframeAPIReady();else{const a=document.createElement("script");a.src="https://www.youtube.com/iframe_api";const i=document.getElementsByTagName("script")[0];i&&i.parentNode?i.parentNode.insertBefore(a,i):document.head.appendChild(a)}return()=>{e.current&&typeof e.current.destroy=="function"&&e.current.destroy(),window.onYouTubeIframeAPIReady=null}},[]);const r=()=>{var a;(a=e.current)==null||a.playVideo()},o=()=>{var a;(a=e.current)==null||a.pauseVideo()},n=()=>{var a;(a=e.current)==null||a.stopVideo()};return s.jsxs("div",{className:"bg-[#ECE9D8] font-tahoma text-sm h-full flex flex-col",children:[s.jsx("div",{className:"flex-1 bg-black flex items-center justify-center",children:s.jsx("iframe",{ref:t,width:"100%",height:"100%",src:"https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&mute=1&controls=0&enablejsapi=1",title:"YouTube video player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),s.jsxs("div",{className:"bg-[#ECE9D8] p-2 border-t-2 border-white flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("button",{className:"xp-button",onClick:r,children:s.jsx(Ia,{size:20})}),s.jsx("button",{className:"xp-button",onClick:o,children:s.jsx(Ra,{size:20})}),s.jsx("button",{className:"xp-button",onClick:n,children:s.jsx(va,{size:20})})]}),s.jsx("div",{className:"w-1/2 h-4 bg-gray-300 border border-gray-400 rounded-full overflow-hidden",children:s.jsx("div",{className:"bg-blue-500 h-full w-1/3"})})]}),s.jsx("div",{className:"text-xs text-center py-1 border-t border-gray-400",children:"Ready"})]})},qc=()=>s.jsxs("div",{className:"p-4 bg-white font-tahoma h-full",children:[s.jsxs("div",{className:"flex items-center mb-4 pb-2 border-b-2 border-blue-500",children:[s.jsx(so,{size:32,className:"text-gray-600 mr-3"}),s.jsx("h2",{className:"text-xl font-bold text-gray-800",children:"Control Panel"})]}),s.jsx("p",{className:"text-gray-700",children:"This is a placeholder for settings and preferences. In a real application, you could control theme settings, accessibility options, or other application-wide configurations here."})]}),Uc=({updateWindowSize:e,windowId:t})=>{const r=u.useRef(null),[o,n]=u.useState(!0),[a,i]=u.useState(0),[l,d]=u.useState(!0);u.useEffect(()=>{const g=setTimeout(()=>{i(20)},100);return()=>clearTimeout(g)},[]);const h=u.useCallback(()=>{i(30),e&&t&&r.current?requestAnimationFrame(()=>{i(60),setTimeout(()=>{if(e&&t&&r.current){const g=r.current.closest(".xp-window");if(g){const p=g.offsetWidth,c=g.offsetHeight;p>0&&c>0&&(i(80),e(t,{width:p+2,height:c+2}),setTimeout(()=>{e&&t&&e(t,{width:p,height:c}),i(90)},100),setTimeout(()=>{if(e&&t&&r.current){const b=r.current;b.offsetHeight>0&&b.offsetWidth>0&&(e(t,{width:p+1,height:c+1}),setTimeout(()=>{e&&t&&e(t,{width:p,height:c}),i(100),setTimeout(()=>{d(!1),setTimeout(()=>{n(!1)},300)},200)},50))}else i(100),setTimeout(()=>{d(!1),setTimeout(()=>{n(!1)},300)},200)},500))}}},200)}):setTimeout(()=>{i(100),setTimeout(()=>{d(!1),setTimeout(()=>{n(!1)},300)},200)},400)},[e,t]);return s.jsxs("div",{className:"h-full flex flex-col bg-gray-100 font-tahoma text-xs relative",children:[s.jsxs("div",{className:"p-1 bg-gray-200 border-b border-gray-300 flex items-center space-x-1 shrink-0",children:[s.jsx("span",{className:"text-gray-500 font-semibold px-2",children:"Address"}),s.jsxs("div",{className:"flex-1 bg-white border border-gray-400 p-0.5 flex items-center",children:[s.jsx("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACLSURBVDhPY2AY6OLiAjo5Of+P4CIgvwHif4z/DAyM6Asa3AyMDCysDEyWpCAg/w9iDAwMjAycgCof4x8YGBgYWBgYjAkZGBgYGFgYWEzAGBj8//9/BgaE/zMy/P9/YGAQZWBgYGD4//+fmYGBgYFB5f/l/7GNgYGBgYHh//n/dUYGBgYGDADGABo4iP2XAAAAAElFTkSuQmCC",alt:"ie icon",className:"h-4 w-4 mr-1"}),s.jsx("input",{type:"text",readOnly:!0,value:"file:///C:/Users/<USER>/Documents/my_resume.pdf",className:"w-full bg-transparent outline-none"})]}),s.jsx("button",{className:"xp-button px-2",children:"Go"})]}),s.jsxs("div",{className:"flex-1 bg-white overflow-hidden relative",children:[s.jsx("iframe",{ref:r,src:"https://drive.google.com/file/d/1XaY0tfH38EOgMk20P-qi3wWz_ZqGTi64/preview?rm=minimal&embedded=true",className:"w-full h-full",style:{border:0},onLoad:h}),o&&s.jsx("div",{className:"absolute inset-0 bg-white flex items-center justify-center z-10 transition-opacity duration-500 ease-out",style:{fontFamily:"Tahoma, sans-serif",opacity:l?1:0},children:s.jsxs("div",{className:"text-center bg-gray-100 border-2 border-gray-400 p-6 rounded shadow-lg",style:{borderTopColor:"#ffffff",borderLeftColor:"#ffffff",borderRightColor:"#808080",borderBottomColor:"#808080",minWidth:"280px"},children:[s.jsx("div",{className:"text-red-600 text-5xl mb-3",children:"📄"}),s.jsx("p",{className:"text-gray-700 text-sm mb-2 font-medium",children:"Adobe Acrobat Reader"}),s.jsxs("p",{className:"text-gray-600 text-xs mb-4",children:[a<40&&"Loading document...",a>=40&&a<70&&"Preparing viewer...",a>=70&&a<95&&"Optimizing display...",a>=95&&"Ready"]}),s.jsx("div",{className:"bg-white border border-gray-400 h-4 w-56 mx-auto overflow-hidden",style:{borderTopColor:"#808080",borderLeftColor:"#808080",borderRightColor:"#ffffff",borderBottomColor:"#ffffff"},children:s.jsx("div",{className:"h-full bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 transition-all duration-200 ease-out relative",style:{width:`${a}%`},children:s.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"})})}),s.jsxs("p",{className:"text-gray-500 text-xs mt-2",children:[a,"%"]})]})})]}),s.jsx("div",{className:"p-2 bg-gray-100 border-t border-gray-300 flex justify-end items-center shrink-0",children:s.jsx("div",{className:"flex items-center space-x-2",children:s.jsxs("a",{href:"https://drive.google.com/uc?export=download&id=1XaY0tfH38EOgMk20P-qi3wWz_ZqGTi64",download:"my_resume.pdf",target:"_blank",className:"xp-button px-4 py-1 flex items-center space-x-2 no-underline text-black",children:[s.jsx(ja,{size:16}),s.jsx("span",{children:"Download Resume"})]})})})]})},Yc=()=>{const e=(r,o)=>{navigator.clipboard.writeText(r),Bi.success(`${o} copied to clipboard.`,{duration:2e3})},t=[{icon:s.jsx(Gt,{size:20}),label:"Email",value:"<EMAIL>",href:"mailto:<EMAIL>"},{icon:s.jsx(za,{size:20}),label:"Phone",value:"(*************",href:"tel:5551234567"},{icon:s.jsx(ro,{size:20}),label:"LinkedIn",value:"linkedin.com/in/johnsmith",href:"https://linkedin.com/in/johnsmith"},{icon:s.jsx(Ea,{size:20}),label:"GitHub",value:"github.com/johnsmith",href:"https://github.com/johnsmith"},{icon:s.jsx(Er,{size:20}),label:"Portfolio",value:"johnsmith.dev",href:"https://johnsmith.dev"}];return s.jsxs("div",{className:"p-4",children:[s.jsx("h2",{className:"text-lg font-bold mb-4",children:"Contact Information"}),s.jsx("div",{className:"space-y-4",children:t.map((r,o)=>s.jsxs("div",{className:"flex items-center space-x-3",children:[r.icon,s.jsxs("div",{className:"flex-grow",children:[s.jsx("p",{className:"font-semibold",children:r.label}),s.jsx("a",{href:r.href,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 cursor-pointer hover:underline",children:r.value})]}),s.jsxs(Dr,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>e(r.value,r.label),title:`Copy ${r.label}`,children:[s.jsx(wa,{size:16}),s.jsxs("span",{className:"sr-only",children:["Copy ",r.label]})]})]},o))})]})},Pt=({name:e,description:t,icon:r,onClick:o})=>s.jsxs("div",{className:"flex flex-col items-center p-3 sm:p-4 cursor-pointer group transition-all duration-300 hover:bg-blue-50 rounded-lg border border-transparent hover:border-blue-200 hover:shadow-lg active:scale-95",onClick:o,onDoubleClick:o,children:[s.jsxs("div",{className:"relative mb-3 group-hover:scale-110 transition-transform duration-300 ease-out",children:[s.jsxs("svg",{width:"72",height:"72",viewBox:"0 0 64 64",className:"drop-shadow-lg group-hover:drop-shadow-xl transition-all duration-300",children:[s.jsx("path",{d:"M8 16 L8 52 C8 54 10 56 12 56 L52 56 C54 56 56 54 56 52 L56 20 C56 18 54 16 52 16 L32 16 L28 12 L12 12 C10 12 8 14 8 16 Z",fill:"#FFD93D",stroke:"#E6C200",strokeWidth:"1.5",className:"group-hover:fill-yellow-300 transition-colors duration-300"}),s.jsx("path",{d:"M8 20 L8 52 C8 54 10 56 12 56 L52 56 C54 56 56 54 56 52 L56 24 C56 22 54 20 52 20 L12 20 C10 20 8 22 8 20 Z",fill:"#FFED4E",stroke:"#E6C200",strokeWidth:"1.5",className:"group-hover:fill-yellow-200 transition-colors duration-300"}),s.jsx("path",{d:"M12 12 L28 12 L32 16 L52 16 C54 16 56 18 56 20 L8 20 C8 18 10 16 12 16 L12 12 Z",fill:"#FFF59D",stroke:"#E6C200",strokeWidth:"1.5",className:"group-hover:fill-yellow-100 transition-colors duration-300"})]}),s.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-2xl sm:text-3xl group-hover:scale-110 transition-transform duration-300",children:r})]}),s.jsxs("div",{className:"text-center max-w-[140px]",children:[s.jsx("h3",{className:"text-sm sm:text-base font-medium text-gray-800 mb-1 group-hover:text-blue-600 transition-colors duration-300 leading-tight",children:e}),s.jsx("p",{className:"text-xs text-gray-500 leading-tight group-hover:text-gray-600 transition-colors duration-300",children:t})]})]}),Mt=({projectId:e,onNavigateBack:t})=>{const[r,o]=u.useState(""),[n,a]=u.useState(!0),[i,l]=u.useState(null);return u.useEffect(()=>{(async()=>{try{a(!0),l(null);const h=await fetch(`/projects/${e}.html`);if(!h.ok)throw new Error(`Failed to load project: ${h.statusText}`);const g=await h.text();o(g)}catch(h){l(h instanceof Error?h.message:"Failed to load project")}finally{a(!1)}})()},[e]),n?s.jsx("div",{className:"h-full bg-white flex items-center justify-center",style:{fontFamily:"Tahoma, sans-serif"},children:s.jsxs("div",{className:"text-center bg-gray-100 border-2 border-gray-400 p-8 rounded",style:{borderTopColor:"#ffffff",borderLeftColor:"#ffffff",borderRightColor:"#808080",borderBottomColor:"#808080"},children:[s.jsx("div",{className:"text-blue-600 text-4xl mb-4",children:"🌐"}),s.jsx("p",{className:"text-gray-700 text-sm mb-2",children:"Internet Explorer"}),s.jsx("p",{className:"text-gray-600 text-xs",children:"Loading project content..."}),s.jsx("div",{className:"mt-4 bg-white border border-gray-400 h-2 w-48 mx-auto overflow-hidden",children:s.jsx("div",{className:"h-full bg-blue-500 animate-pulse"})})]})}):i?s.jsx("div",{className:"h-full bg-white flex items-center justify-center",style:{fontFamily:"Tahoma, sans-serif"},children:s.jsxs("div",{className:"text-center bg-gray-100 border-2 border-gray-400 p-8 rounded max-w-md",style:{borderTopColor:"#ffffff",borderLeftColor:"#ffffff",borderRightColor:"#808080",borderBottomColor:"#808080"},children:[s.jsx("div",{className:"text-red-600 text-4xl mb-4",children:"❌"}),s.jsx("h3",{className:"text-sm font-bold text-gray-800 mb-2",children:"Internet Explorer"}),s.jsx("p",{className:"text-xs text-gray-600 mb-4",children:"The page cannot be displayed"}),s.jsx("p",{className:"text-xs text-gray-500 mb-4",children:i}),s.jsx("button",{onClick:()=>window.location.reload(),className:"px-3 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all",style:{borderTopColor:"#ffffff",borderLeftColor:"#ffffff",borderRightColor:"#808080",borderBottomColor:"#808080"},children:"Refresh"})]})}):s.jsxs("div",{className:"h-full bg-white flex flex-col",children:[s.jsx("div",{className:"bg-gradient-to-b from-blue-50 to-blue-100 border-b-2 border-gray-400 px-3 py-2",style:{fontFamily:"Tahoma, sans-serif"},children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[t&&s.jsx("button",{onClick:t,className:"px-2 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all",style:{borderTopColor:"#ffffff",borderLeftColor:"#ffffff",borderRightColor:"#808080",borderBottomColor:"#808080"},title:"Back to Projects",children:"← Back"}),s.jsx("button",{onClick:()=>window.location.reload(),className:"px-2 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all",style:{borderTopColor:"#ffffff",borderLeftColor:"#ffffff",borderRightColor:"#808080",borderBottomColor:"#808080"},title:"Refresh",children:"🔄"}),s.jsx("button",{className:"px-2 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all",style:{borderTopColor:"#ffffff",borderLeftColor:"#ffffff",borderRightColor:"#808080",borderBottomColor:"#808080"},title:"Home",children:"🏠"})]}),s.jsxs("div",{className:"flex-1 flex items-center gap-2",children:[s.jsx("span",{className:"text-xs text-gray-700",children:"Address"}),s.jsxs("div",{className:"flex-1 bg-white border-2 border-gray-400 px-2 py-1 text-xs font-mono",style:{borderTopColor:"#808080",borderLeftColor:"#808080",borderRightColor:"#ffffff",borderBottomColor:"#ffffff"},children:["file:///C:/Projects/",e,".html"]}),s.jsx("button",{className:"px-2 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all",style:{borderTopColor:"#ffffff",borderLeftColor:"#ffffff",borderRightColor:"#808080",borderBottomColor:"#808080"},children:"Go"})]})]})}),s.jsx("div",{className:"flex-1 overflow-auto bg-white",children:s.jsx("div",{className:"h-full w-full",style:{fontFamily:"Tahoma, Arial, sans-serif"},children:s.jsx("iframe",{srcDoc:r,className:"w-full h-full border-0",title:`Project: ${e}`,sandbox:"allow-same-origin allow-scripts"})})}),s.jsxs("div",{className:"bg-gradient-to-b from-gray-100 to-gray-200 border-t-2 border-gray-400 px-3 py-1 text-xs text-gray-700 flex items-center justify-between",style:{fontFamily:"Tahoma, sans-serif",borderTopColor:"#ffffff"},children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("span",{children:"✅ Done"}),s.jsx("span",{children:"📄 Document: Complete"})]}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("div",{className:"bg-white border border-gray-400 px-2 py-0.5 text-xs",style:{borderTopColor:"#808080",borderLeftColor:"#808080",borderRightColor:"#ffffff",borderBottomColor:"#ffffff"},children:"🌐 Internet Zone"})})]})]})},Kc=(e,t)=>{const r=typeof t=="function"?t:t.onOpenWindow,o=typeof t=="object"?t.updateWindowSize:void 0,n=typeof t=="object"?t.windowId:void 0;switch(e){case"recyclebin":return s.jsx(Zl,{});case"my_computer":return s.jsx(Bc,{});case"skills":return s.jsx(_c,{});case"my_documents":return s.jsx(Hc,{onOpenWindow:r});case"my_pictures":return s.jsx($c,{});case"my_music":return s.jsx(Gc,{});case"control_panel":return s.jsx(qc,{});case"certifications":return s.jsxs("div",{className:"p-4",children:[s.jsx("h2",{className:"text-lg font-bold mb-4",children:"Certifications"}),s.jsx("p",{children:"This is a placeholder for certifications. I would list my professional certifications here, if I had any."}),s.jsxs("ul",{className:"list-disc list-inside mt-2",children:[s.jsx("li",{children:"Example: Awesome Developer Certificate"}),s.jsx("li",{children:"Example: Certified Tech Guru"})]})]});case"projects":return s.jsxs("div",{className:"p-4 sm:p-6 bg-gradient-to-br from-gray-50 to-blue-50 min-h-full",children:[s.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-5xl mx-auto justify-items-center",children:[s.jsx(Pt,{name:"Zendo",description:"Browser Extension",icon:"🧘",onClick:()=>r("project-zendo","Zendo - Browser Extension")}),s.jsx(Pt,{name:"Chromepanion",description:"Browser Extension",icon:"🤖",onClick:()=>r("project-chromepanion","Chromepanion - Browser Extension")}),s.jsx(Pt,{name:"Moverzz",description:"Rental Mobile App",icon:"🚚",onClick:()=>r("project-moverzz","Moverzz - Rental Mobile App")}),s.jsx(Pt,{name:"E-Commerce Platform",description:"Full-stack Web App",icon:"🛍️",onClick:()=>r("project-ecommerce","E-Commerce Platform")})]}),s.jsx("div",{className:"mt-8 text-center",children:s.jsx("div",{className:"inline-block bg-white/70 backdrop-blur-sm rounded-lg px-4 py-2 border border-blue-200",children:s.jsxs("p",{className:"text-sm text-gray-600",children:["💡 ",s.jsx("strong",{children:"Tip:"})," Click on any project folder to explore detailed information"]})})})]});case"my_resume":return s.jsx(Uc,{updateWindowSize:o,windowId:n});case"contact":return s.jsx(Yc,{});case"about":return s.jsxs("div",{className:"p-4 bg-white font-mono text-sm",children:[s.jsx("div",{className:"border-b pb-2 mb-4",children:s.jsx("span",{className:"font-bold",children:"About Me.txt"})}),s.jsx("div",{className:"whitespace-pre-line",children:`Hello! I'm John Smith, a passionate software engineer with over 5 years of experience building web applications and scalable systems.

What I Do:
- Full-stack web development
- Cloud architecture and DevOps
- Mobile app development
- System design and optimization

My Approach:
I believe in writing clean, maintainable code and creating solutions that truly serve users' needs. I'm always excited to learn new technologies and tackle challenging problems.

Current Focus:
- Microservices architecture
- AI/ML integration
- Performance optimization
- Team leadership and mentoring

When I'm not coding, you can find me:
- Contributing to open source projects
- Reading about emerging technologies
- Playing video games (yes, including retro ones!)
- Hiking and photography

Thanks for visiting my Windows XP themed portfolio! This nostalgic interface showcases my skills while paying homage to the golden age of computing. 

Feel free to explore my projects and get in touch if you'd like to collaborate!

---
Last modified: 2024
File size: 1.2 KB`})]});case"project-zendo":return s.jsx(Mt,{projectId:"zendo",onNavigateBack:()=>r("projects","My Projects")});case"project-chromepanion":return s.jsx(Mt,{projectId:"chromepanion",onNavigateBack:()=>r("projects","My Projects")});case"project-moverzz":return s.jsx(Mt,{projectId:"moverzz",onNavigateBack:()=>r("projects","My Projects")});case"project-ecommerce":return s.jsx(Mt,{projectId:"ecommerce",onNavigateBack:()=>r("projects","My Projects")});default:return s.jsx("div",{className:"p-4",children:"Window content not found."})}},Qc=e=>{const[t,r]=u.useState([]),[o,n]=u.useState(1e3),a=(x,m)=>{const y=[...dn,...un].find(j=>j.id===x);if(t.find(j=>j.id===x)){r(j=>j.map(S=>S.id===x?{...S,isMinimized:!1,zIndex:o+1}:S)),n(j=>j+1);return}const w={id:x,title:m,icon:(y==null?void 0:y.icon)||"📄",content:Kc(x,{onOpenWindow:a,updateWindowSize:c,windowId:x}),isMinimized:!1,position:e?{x:0,y:0}:{x:200+t.length*30,y:50+t.length*30},size:e?{width:globalThis.window.innerWidth,height:globalThis.window.innerHeight-40}:x==="my_resume"?{width:1200,height:800}:x==="my_computer"?{width:900,height:800}:x==="recyclebin"?{width:900,height:800}:x==="my_music"?{width:900,height:800}:x==="my_pictures"?{width:900,height:800}:x.startsWith("project-")?{width:900,height:800}:{width:900,height:800},zIndex:o+1,isMaximized:!!e};r(j=>[...j,w]),n(j=>j+1)},i=x=>{r(m=>m.filter(f=>f.id!==x))},l=x=>{r(m=>m.map(f=>f.id===x?{...f,isMinimized:!0}:f))},d=x=>{e||(r(m=>m.map(f=>f.id===x?{...f,originalPosition:f.isMaximized?f.originalPosition:f.position,originalSize:f.isMaximized?f.originalSize:f.size,position:{x:0,y:0},size:{width:window.innerWidth,height:window.innerHeight-40},zIndex:o+1,isMaximized:!0}:f)),n(m=>m+1))},h=x=>{e||(r(m=>m.map(f=>f.id===x&&f.originalPosition&&f.originalSize?{...f,position:f.originalPosition,size:f.originalSize,zIndex:o+1,isMaximized:!1}:f)),n(m=>m+1))},g=x=>{const m=t.find(f=>f.id===x);m&&m.zIndex===o||(r(f=>f.map(y=>y.id===x?{...y,zIndex:o+1}:y)),n(f=>f+1))},p=(x,m)=>{r(f=>f.map(y=>y.id===x?{...y,position:m}:y))},c=(x,m)=>{r(f=>f.map(y=>y.id===x?{...y,size:m}:y))};return{openWindows:t,openWindow:a,closeWindow:i,minimizeWindow:l,maximizeWindow:d,restoreWindow:h,bringToFront:g,updateWindowPosition:p,updateWindowSize:c,handleTaskbarClick:x=>{const m=t.find(f=>f.id===x);m!=null&&m.isMinimized?(r(f=>f.map(y=>y.id===x?{...y,isMinimized:!1,zIndex:o+1}:y)),n(f=>f+1)):g(x)}}},Xc="/static/media/windows-xp-bliss.DypUyfA3.jpg",Jc=()=>{const[e,t]=u.useState(!1),[r,o]=u.useState(!0),{isShuttingDown:n,shutdownType:a,triggerShutdown:i,resetToDesktop:l}=ql(),d=Ul(),{openWindows:h,openWindow:g,closeWindow:p,minimizeWindow:c,maximizeWindow:b,restoreWindow:x,bringToFront:m,updateWindowPosition:f,updateWindowSize:y,handleTaskbarClick:N}=Qc(d);return n?s.jsx(Gl,{type:a,onComplete:l}):s.jsxs("div",{className:"h-screen w-screen relative bg-cover bg-center bg-no-repeat",style:{backgroundImage:`url(${Xc})`},onClick:()=>t(!1),children:[s.jsx("div",{className:"absolute top-0 left-0 right-0 bottom-10 p-4 overflow-hidden",children:s.jsx("div",{className:"flex flex-col flex-wrap content-start h-full gap-y-4",children:dn.map(w=>s.jsx(Il,{icon:w,onDoubleClick:()=>g(w.id,w.name)},w.id))})}),h.map(w=>!w.isMinimized&&s.jsx(Ll,{window:w,onClose:()=>p(w.id),onMinimize:()=>c(w.id),onMaximize:()=>b(w.id),onRestore:()=>x(w.id),onBringToFront:()=>m(w.id),onUpdatePosition:j=>f(w.id,j),onUpdateSize:j=>y(w.id,j),isMobile:d},w.id)),r&&s.jsx(Yl,{onClose:()=>o(!1),onOpenWindow:g}),e&&s.jsx($l,{menuItems:un,onClose:()=>t(!1),onOpenWindow:g,onShutdown:i}),s.jsx(zl,{openWindows:h,onStartClick:()=>t(!e),onOpenWindow:g,onWindowClick:N})]})},Zc=()=>{const[e,t]=u.useState(!0);return u.useEffect(()=>{const r=setTimeout(()=>{t(!1)},2e3);return()=>clearTimeout(r)},[]),s.jsx("div",{className:"h-screen w-screen overflow-hidden",children:e?s.jsx(V0,{onBootComplete:()=>t(!1)}):s.jsx(Jc,{})})},e1=()=>{const e=_n();return u.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:s.jsxs("div",{className:"text-center",children:[s.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),s.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),s.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},t1=new L0,r1=()=>s.jsx(B0,{client:t1,children:s.jsxs(p0,{children:[s.jsx(ji,{}),s.jsx(Ji,{}),s.jsx(Bn,{children:s.jsxs(Vn,{children:[s.jsx(Ur,{path:"/",element:s.jsx(Zc,{})}),s.jsx(Ur,{path:"*",element:s.jsx(e1,{})})]})})]})});As(document.getElementById("root")).render(s.jsx(r1,{}));
